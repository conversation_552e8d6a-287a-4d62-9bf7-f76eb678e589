"use client";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Search } from "lucide-react";

export const ShrinkableSearchButton = () => {
  const [isHovered, setIsHovered] = useState<boolean>(false);
  return (
    <motion.div
      whileHover={{
        width: "396px",
        borderRadius: "8px",
        transition: { duration: 0.3, ease: "easeOut" },
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className="flex h-9 items-center justify-start rounded-full border border-[#6366F1] bg-[#041F3F] p-2"
    >
      <Search className="h-9 w-5 flex-shrink-0 cursor-pointer text-[#1A7EF2]" />
      <AnimatePresence>
        {isHovered && (
          <motion.input
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: "100%", opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            autoFocus
            type="text"
            placeholder="Search Keywords"
            onBlur={() => setIsHovered(false)}
            className="ml-2 h-full flex-grow border-none bg-transparent p-0 text-base text-[var(--dark-text)] placeholder-[var(--dark-text-muted)] outline-none focus:ring-0"
          />
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export const ShrinkableAddButton = () => {
  const [isHovered, setIsHovered] = useState<boolean>(false);
  return (
    <motion.div
      whileHover={{ width: "200px", borderRadius: "4px" }}
      transition={{ duration: 0.4, ease: "easeInOut" }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className="flex h-11 w-11 cursor-pointer items-center justify-center gap-2 overflow-hidden rounded-full bg-[var(--color-company)] p-3 text-[var(--dark-text)]"
    >
      <Plus className="h-5 w-5 text-[var(--dark-text)]" />

      {isHovered && (
        <span className="text-base font-medium text-nowrap text-[var(--dark-text)]">
          Post Thought
        </span>
      )}
    </motion.div>
  );
};
