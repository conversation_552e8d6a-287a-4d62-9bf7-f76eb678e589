"use client";
import {
  Comment<PERSON>utton,
  LikeButton,
  ShareButton,
} from "@/components/ui/common/buttons";
import { Container } from "@/components/ui/common/container";
import {
  Book,
  BookOpen,
  CircleQuestionMark,
  Lightbulb,
  User,
} from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { communityPosts } from "../constants";
import { CommunityPostProps } from "@/types/community";

const CommunityPosts = () => {
  return (
    <>
      {communityPosts.map((post) => (
        <CommunityPostsCards key={post.id} {...post} />
      ))}
    </>
  );
};

export default CommunityPosts;

export const CommunityPostsCards = ({
  title,
  content,
  chart_image,
  user,
  hastags,
  tags,
}: CommunityPostProps) => {
  //   const HshtagList = ["#Breakout", "#Banking", "#Finance", "#StockMarket"];
  const { user_image, firstname, lastname, is_verified } = user || {};
  return (
    <Container className="mb-3 overflow-hidden rounded-lg border border-[#1F2937]">
      <header className="p-4">
        {/* Avatar Container Section */}
        <div className="flex items-center gap-2">
          {user_image ? (
            <Image
              src={user_image}
              alt={firstname || "User avatar"}
              height={35}
              width={35}
              className="h-9 w-9 shrink-0 overflow-hidden rounded-full object-cover"
            />
          ) : (
            <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-full bg-[#1F2937]">
              <User className="h-5 w-5 text-[var(--dark-text-muted)]" />
            </div>
          )}
          <div className="relative">
            <div className="flex items-center">
              <h1 className="text-sm">
                {firstname} {lastname}
              </h1>
              {is_verified && (
                <Image
                  src="/community/verified.svg"
                  alt="verified prfoile"
                  width={14}
                  height={14}
                  className="ml-1"
                />
              )}

              <p className="ml-3 flex items-center gap-1 rounded-full bg-[#1F2937] px-2 py-1 text-xs text-[var(--dark-text)]">
                {tags === "idea" && (
                  <Lightbulb className="h-4 w-4 text-[var(--color-warning)]" />
                )}
                {tags === "question" && (
                  <CircleQuestionMark className="h-4 w-4 text-[var(--color-warning)]" />
                )}
                {tags === "articles" && (
                  <Book className="h-4 w-4 text-[var(--color-warning)]" />
                )}
                {tags === "experiences" && (
                  <BookOpen className="h-4 w-4 text-[var(--color-warning)]" />
                )}
                {tags}
              </p>
            </div>
            <span className="text-xs text-[var(--dark-text-muted)]">
              2 hours ago
            </span>
          </div>
        </div>

        {/* Title & Description Sections */}
        <div className="my-4">
          <h1 className="line-clamp-1 text-xl font-medium text-[var(--dark-text)]">
            {title}
          </h1>
          <p className="mt-2 text-sm font-normal text-[var(--dark-text-muted)]">
            {content}
          </p>
        </div>
      </header>

      <main className="px-4">
        {chart_image && (
          <div className="mb-4">
            <Image
              src={chart_image}
              alt={title}
              height={328}
              width={795}
              className="h-[328px] w-full rounded-lg border border-[var(--dark-border)] object-cover"
            />
          </div>
        )}

        {/* Posts Hastags Lists */}

        <div className="flex flex-wrap gap-2">
          {hastags.map((tag) => (
            <span
              key={tag}
              className="rounded-sm bg-[#19212C] px-[10px] py-1 text-xs text-[var(--dark-text)]"
            >
              {tag}
            </span>
          ))}
        </div>
      </main>

      {/* Reaction Section For Posts */}
      <footer className="flex items-center justify-start gap-4 px-6 py-4">
        <LikeButton />
        <CommentButton />
        <ShareButton />
      </footer>
    </Container>
  );
};
