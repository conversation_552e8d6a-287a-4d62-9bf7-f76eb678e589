// src/types/post.ts

export interface CommunityPostProps {
  id: number;
  title: string;
  content: string;
  chart_image: string | null;
  tags: string;
  hastags: Array<string>;
  user: {
    id: number;
    firstname: string;
    lastname: string;
    user_image: string;
    is_verified: boolean;
  };
  created_at: string; // ISO date string
}

export interface TopContributorProps {
  idx: number;
  image: string;
  name: string;
  contributions: number;
}

export interface TopHastagsProps {
  hashtag: string;
  count: number;
}
