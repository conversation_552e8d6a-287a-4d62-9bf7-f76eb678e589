"use client";
import { TopContributorProps, TopHastagsProps } from "@/types/community";
import Image from "next/image";
import { topContributors, topHastagsData } from "../constants";
import { SecondaryButton } from "@/components/ui/common/buttons";

const CommunitySideBar = () => {
  return (
    <div className="space-y-5">
      <TopContributors />
      <TopHastags />
      <NoticeSection />
    </div>
  );
};

export default CommunitySideBar;

export const TopContributors = () => {
  return (
    <div className="border border-[var(--dark-border)]">
      {/* Sidebar Header */}
      <div className="flex items-center gap-2.5 border-b border-[var(--dark-border)] p-3">
        <Image
          src="/community/badge.svg"
          alt="Badge "
          width={20}
          height={20}
          className="inline-block"
        />
        <h1 className="text-base font-medium text-[var(--dark-text)]">
          Top Contributors
        </h1>
      </div>

      {/* Top Contributors Users Container */}
      {topContributors.map((contributors, idx) => (
        <TopContributorsCards key={idx} idx={idx + 1} {...contributors} />
      ))}

      <button
        onClick={() => alert("Button Pressed View All")}
        className="w-full cursor-pointer rounded-sm bg-transparent p-3 text-sm text-[var(--dark-text)]"
      >
        View All
      </button>
    </div>
  );
};

export const TopContributorsCards = ({
  idx,
  name,
  image,
  contributions,
}: TopContributorProps) => {
  return (
    <div className="px-3 py-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Image
            src={image}
            alt={name}
            width={30}
            height={30}
            className="inline-block rounded-full object-cover"
          />
          <div>
            <h1 className="text-base text-[var(--dark-text)]">{name}</h1>
            <p className="text-xs text-[var(--dark-text-muted)]">
              {contributions} contributions
            </p>
          </div>
        </div>
        <p className="text-xs text-[#6366F1]">#{idx}</p>
      </div>
    </div>
  );
};

export const TopHastags = () => {
  return (
    <div className="border border-[var(--dark-border)]">
      {/* Sidebar Header */}
      <div className="flex items-center gap-2.5 border-b border-[var(--dark-border)] p-3">
        <Image
          src="/community/badge.svg"
          alt="Badge "
          width={20}
          height={20}
          className="inline-block"
        />
        <h1 className="text-base font-medium text-[var(--dark-text)]">
          Trending Topics
        </h1>
      </div>

      {/* Top Contributors Users Container */}
      <div className="flex flex-wrap gap-2 overflow-hidden p-2">
        {topHastagsData.map((contributors, idx) => (
          <TopHastagsCards key={idx} {...contributors} />
        ))}
      </div>
    </div>
  );
};

export const TopHastagsCards = ({ hashtag, count }: TopHastagsProps) => {
  return (
    <div className="items flex justify-between rounded-[40px] bg-[#233041] px-2 py-1">
      <p className="text-sm text-[var(--dark-texxt)]">#{hashtag}</p>
      <span className="rounded-full bg-[#1A232F] px-2 py-1 text-xs text-[#6366F1]">
        {count}
      </span>
    </div>
  );
};

export const NoticeSection = () => {
  return (
    <div
      className="rounded-lg p-6"
      style={{
        background: "var(--company-linear)",
      }}
    >
      <div className="flex flex-col items-center justify-center gap-3">
        <h5 className="text-base font-medium text-[var(--dark-text)]">
          Enhance Your Trading
        </h5>
        <p className="text-sm text-[var(--dark-text)]">
          Take your NEPSE trading skills to the next level with our expert-led
          courses.
        </p>
        <button className="w-full" onClick={() => alert("Button Pressed")}>
          <SecondaryButton label="Login" className="w-full" />
        </button>
      </div>
    </div>
  );
};
