import { Container } from "@/components/ui/common/container";
import {
  Bookmark,
  CtaBearish,
  CtaBullish,
  CtaCategory,
  CtaIndustry,
  CtaNeutral,
  Reset,
} from "@/components/ui/common/ctaButtons";
import { ctaCategoryData } from "../constants";

const NewsFilterSection = () => {
  return (
    <div className="mt-6 border-y border-[var(--dark-border)] bg-[var(--dark-surface)] px-6">
      <Container className="py-3">
        <div className="flex justify-between">
          <div className="flex gap-4">
            <p className="text-xs text-[var(--dark-text)]">Filters:</p>
            <div className="grid grid-cols-2 gap-2 text-center">
              {ctaCategoryData.map((item, index) => (
                <CtaCategory key={index} label={item} />
              ))}
            </div>
          </div>
          <div className="flex gap-1">
            <CtaBullish />
            <CtaBearish />
            <CtaNeutral />
          </div>
          <div className="flex gap-1">
            {ctaCategoryData.map((item, index) => (
              <CtaIndustry key={index} label={item} />
            ))}
          </div>

          <div className="flex flex-col justify-center gap-2">
            <Reset />
            <Bookmark />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default NewsFilterSection;
