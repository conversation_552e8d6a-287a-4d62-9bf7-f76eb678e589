// src/lib/mock-data.ts

import { Post } from "@/types/community";

// We'll use this array as our "in-memory" database.
// 'let' allows us to modify it (e.g., add new posts).
export let mockPosts: Post[] = [
  {
    id: 1,
    title: "Mock Post: Introduction to Candlesticks",
    content:
      "This is the content for our first mock post about Japanese candlesticks. It helps visualize price movements.",
    chart_image:
      "https://via.placeholder.com/800x400.png?text=Sample+Chart+Image",
    analysis_text: "The analysis shows a clear bullish trend.",
    author: {
      id: 99,
      username: "mockAdmin",
    },
    created_at: new Date().toISOString(),
  },
  {
    id: 2,
    title: "Mock Post: Understanding Moving Averages",
    content:
      "A moving average is a commonly used technical indicator that smooths out price data by creating a constantly updated average price.",
    chart_image: null,
    analysis_text: "The 50-day MA is about to cross the 200-day MA.",
    author: {
      id: 100,
      username: "mockUser",
    },
    created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
  },
];
