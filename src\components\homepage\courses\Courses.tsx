"use client";

import { coursesData } from "@/app/constants";
import { ArrowRight } from "lucide-react";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import { Swiper, SwiperSlide } from "swiper/react";
import CourseCard from "./CourseCard";

const Courses = () => {

  return (
    <div className="mt-10 px-4 sm:px-6 md:px-12">
      {/* Header */}
      <div className="flex flex-col items-center gap-4 text-center text-white">
        <h1 className="text-3xl font-bold">
          Learn to{" "}
          <span className="bg-gradient-to-r from-[#1A7EF2] to-[#6366F1] bg-clip-text text-transparent">
            Trade Confidently
          </span>
        </h1>
        <p className="max-w-3xl text-lg">
          From beginner basics to advanced strategies, our expert-led courses
          will help you navigate the NEPSE market with confidence.
        </p>
      </div>

      {/* Swiper Cards */}
      <div className="mt-6 overflow-hidden">
        <Swiper
          grabCursor={true}
          spaceBetween={25}
          breakpoints={{
            320: { slidesPerView: 1.1 },
            640: { slidesPerView: 1.5 },
            768: { slidesPerView: 2.2 },
            1024: { slidesPerView: 3.5 },
          }}
          className="overflow-visible"
        >
          {coursesData.map((course) => (
            <SwiperSlide key={course.id}>
              <CourseCard {...course} />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* View More Button */}
      <div className="mt-6 flex justify-end">
        <button className="group flex items-center gap-2 rounded-lg bg-transparent px-4 py-2 transition-colors duration-400 hover:bg-[var(--dark-border)]">
          <span className="font-medium text-white group-hover:bg-gradient-to-r group-hover:from-[#1A7EF2] group-hover:to-[#6366F1] group-hover:bg-clip-text group-hover:text-transparent">
            View More
          </span>
          <ArrowRight
            size={18}
            className="text-white transition-colors duration-300 group-hover:text-[#1A7EF2]"
          />
        </button>
      </div>
    </div>
  );
};

export default Courses;
