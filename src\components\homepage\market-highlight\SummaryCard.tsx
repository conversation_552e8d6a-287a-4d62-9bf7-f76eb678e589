import { summaryData } from "@/app/constants";
import { SummaryItem } from "@/types/homepage";

const SummaryCard = () => {
  const getColorClass = (item: SummaryItem) => {
    switch (item.color) {
      case "green":
        return "text-[var(--color-success)]";
      case "red":
        return "text-[var(--color-error)]";
      default:
        return "text-gray-300";
    }
  };

  return (
    <div className="w-full rounded-xl border border-[var(--dark-border)] bg-[#111827] p-6 text-white">
      {/* Card Header */}
      <h1 className="text-xl font-bold">Today's Summary</h1>

      {/* Summary List */}
      <div className="mt-4 flex flex-col gap-4">
        {summaryData.map((item) => (
          <div
            key={item.label}
            className="flex items-center justify-between text-sm"
          >
            {/* Label on the left */}
            <p className={getColorClass(item)}>{item.label}</p>

            {/* Value on the right */}
            <p className={`font-medium ${getColorClass(item)}`}>{item.value}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SummaryCard;
