"use client";
import { useState } from "react";
import { motion } from "motion/react";
import { Book, BookOpen, CircleQuestionMark, Lightbulb } from "lucide-react";

export const TopMultiSelectButton = () => {
  const [isChartSelected, setIsChartSelected] = useState<Array<string>>([]);
  const topSelectionButtonData = [
    { label: "Trade Ideas", icon: Lightbulb },
    { label: "Questions", icon: CircleQuestionMark },
    { label: "Articles", icon: Book },
    { label: "Experiences", icon: BookOpen },
  ];

  // Chart Buttons Selection Handler Function
  const ChartSelectionHandler = (item: string) => {
    if (isChartSelected.includes(item)) {
      setIsChartSelected(isChartSelected.filter((i) => i !== item));
    } else {
      setIsChartSelected([...isChartSelected, item]);
    }
  };
  return (
    <>
      {/* Container Data */}
      <div className="w-full space-x-1 text-center sm:text-start">
        {topSelectionButtonData.map((item, idx) => {
          const Icon = item.icon;
          return (
            <button
              key={idx}
              type="button"
              onClick={() => ChartSelectionHandler(item.label)}
            >
              <motion.div
                animate={{
                  background: isChartSelected.includes(item.label)
                    ? "#121821"
                    : "transparent",
                  border: isChartSelected.includes(item.label)
                    ? "1px solid #6366F1"
                    : "1px solid transparent",
                }}
                whileHover={{ background: "#4F65844D" }}
                className={`flex flex-wrap items-center justify-center gap-2 rounded-lg px-2 py-2.5 text-center text-xs font-normal text-[var(--dark-text)] sm:px-2.5`}
              >
                <Icon className="h-5 w-5" />

                {item.label}
              </motion.div>
            </button>
          );
        })}
      </div>
    </>
  );
};

export const BottomMultiSelectButton = () => {
  const [isChartSelected, setIsChartSelected] = useState<Array<string>>([]);
  const bottomSelectionData = [
    { label: "Strategy" },
    { label: "Knowledge" },
    { label: "Today" },
    { label: "1 week" },
    { label: "1 months" },
  ];

  // Chart Buttons Selection Handler Function
  const ChartSelectionHandler = (item: string) => {
    if (isChartSelected.includes(item)) {
      setIsChartSelected(isChartSelected.filter((i) => i !== item));
    } else {
      setIsChartSelected([...isChartSelected, item]);
    }
    console.log(isChartSelected);
  };
  return (
    <>
      {/* Container Data */}
      <div className="mt-2 space-y-1.5 space-x-1.5 overflow-x-auto text-center sm:text-start">
        {bottomSelectionData.map((item, idx) => (
          <button
            key={idx}
            type="button"
            onClick={() => ChartSelectionHandler(item.label)}
          >
            <motion.div
              animate={{
                background: isChartSelected.includes(item.label)
                  ? "#121821"
                  : "transparent",
                border: isChartSelected.includes(item.label)
                  ? "1px solid #6366F1"
                  : "1px solid transparent",
              }}
              whileHover={{ background: "#4F65844D" }}
              className={`flex flex-wrap items-center justify-center rounded-lg px-2 py-1.5 text-center text-xs font-normal text-[var(--dark-text)] sm:px-2.5`}
            >
              {item.label}
            </motion.div>
          </button>
        ))}
      </div>
    </>
  );
};
