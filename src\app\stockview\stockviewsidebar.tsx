import { PrimaryButton, SecondaryButton } from "@/components/ui/common/buttons";
import React from "react";
import { Inter } from "next/font/google";
import {
  FaceBookIcon,
  TwitterIcon,
  TelegramIcon,
  ShareIcon,
} from "@/components/ui/common/socialmedia";
import Image from "next/image";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const StockViewSidebar = () => {
  return (
    <>
      {/* Top Side bar Section */}
      <div className="rounded-lg border border-[var(--dark-border)] bg-[var(--dark-surface)] p-4">
        <div className="mt-4 mb-14 space-y-4">
          <PrimaryButton label="Add to Watchlist" icon={"/common/star.svg"} />
          <SecondaryButton label="Login" icon={"/common/reminder.svg"} />
        </div>

        <p className={`${inter.className} text-sm text-[#9CA3AF]`}>
          Share this stock
        </p>
        <div className="mt-2 flex items-center gap-2">
          <FaceBookIcon />
          <TwitterIcon />
          <TelegramIcon />
          <ShareIcon />
        </div>
      </div>

      {/* Bottom Side Bar Section */}
      <div className="mt-8 rounded-lg border border-[var(--dark-border)] bg-[var(--dark-surface)] p-4">
        <h2
          className={`${inter.className} flex items-center gap-2 text-xs text-[var(--dark-text)]`}
        >
          <Image
            src="/stockview/market.svg"
            alt="icons"
            width={20}
            height={20}
          />
          Market Overview
        </h2>

        {/* Nepse Index Container */}
        <div className="mt-6">
          <div className="mt-3 flex items-center justify-between">
            <h5 className="text-xs text-[var(--dark-text-muted)]">
              NEPSE Index
            </h5>
            <p className="text-sm text-[var(--dark-text)]">2,150.45</p>
          </div>
          <div className="mt-3 flex items-center justify-between">
            <h5 className="text-xs text-[var(--dark-text-muted)]">
              Banking Index
            </h5>
            <p className="text-sm text-[var(--color-success)]">+1.2%</p>
          </div>
          <div className="mt-3 flex items-center justify-between">
            <h5 className="text-xs text-[var(--dark-text-muted)]">
              Market Status
            </h5>
            <p className="text-sm text-[var(--dark-text)]">Open</p>
          </div>

          {/* Button to View all */}
          <button className="mt-5 text-sm text-[var(--dark-text)]">
            View All
          </button>
        </div>
      </div>
    </>
  );
};

export default StockViewSidebar;
