import { roadmapSteps } from "@/app/constants";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const Roadmap = () => {
  return (
    <div className="mt-16 bg-[#1A1A1A1A] p-8">
      <div className="container grid grid-cols-1 items-center gap-12 rounded-lg border-2 border-[var(--dark-border)] bg-[#1A1A1A1A] p-6 md:grid-cols-2 md:p-8">
        {/* Left Content Column */}
        <div className="flex flex-col">
          {/* Header */}
          <div className="flex items-center gap-3">
            <Image
              src="/roadmap/logo-roadmap.svg"
              alt="logo"
              width={27.5}
              height={15}
            />
            <h1 className="text-2xl font-bold text-[var(--dark-text)]">
              Beginner's Roadmap
            </h1>
          </div>

          {/* Subtitle Paragraph */}
          <p className="mt-4 text-sm text-[var(--dark-text-muted)]">
            New to NEPSE trading? Follow our step-wise-step roadmap designed to
            take you from complete beginner to confident trader.
          </p>

          {/* List of Roadmap Steps */}
          <div className="mt-10 flex flex-col gap-6">
            {roadmapSteps.map((step, index) => (
              <div
                key={index}
                className="flex items-start gap-4 text-[var(--dark-text)]"
              >
                <ArrowRight className="mt-1 h-5 w-5 flex-shrink-0 text-[var(--color-company)]" />
                <div>
                  <h3 className="text-lg">{step.title}</h3>
                  <p className="mt-1 text-sm">{step.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Button */}
          <div className="mt-10">
            <Link
              href="#"
              className="inline-block rounded-sm bg-[var(--color-company)] p-3 text-base font-semibold text-[var(--dark-text)] transition duration-300 hover:bg-blue-700"
            >
              Start Your Journey
            </Link>
          </div>
        </div>

        {/* Right Image Column (Hidden on small screens) */}
        <div className="relative hidden h-80 min-h-[400px] w-full rounded-lg md:block md:h-full">
          <Image
            src="/roadmap/roadmap-beginner.jpg"
            alt="Trader analyzing stock market charts on a computer"
            layout="fill"
            objectFit="cover"
            className="rounded-lg"
          />
        </div>
      </div>
    </div>
  );
};

export default Roadmap;
