"use client";

import Image from "next/image";
import Link from "next/link";
import { Facebook, Twitter, Instagram, Youtube, Mail } from "lucide-react";

const quickLinks = [
  { href: "#", label: "Dashboard" },
  { href: "#", label: "Market Data" },
  { href: "#", label: "News" },
  { href: "#", label: "Courses" },
  { href: "#", label: "Community" },
];

const resourcesLinks = [
  { href: "#", label: "Beginner's Guide" },
  { href: "#", label: "Technical Analysis" },
  { href: "#", label: "Fundamental Analysis" },
  { href: "#", label: "Trading Strategies" },
  { href: "#", label: "Help Center" },
];

const Footer = () => {
  return (
    <footer className="w-full bg-[var(--light-surface)] px-10 py-10 text-[var(--light-text)] dark:bg-[var(--dark-surface)] dark:text-[var(--dark-text)]">
      <div className="container max-w-full sm:px-8">
        <div className="grid grid-cols-1 gap-12 md:grid-cols-4">
          {/* Column 1: Logo and Socials */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center gap-2">
              <div className="relative h-10 w-10">
                {/* Light mode logo */}
                <Image
                  src="/logo-light.svg"
                  alt="NEP X Logo"
                  fill
                  className="block dark:hidden"
                />
                {/* Dark mode logo */}
                <Image
                  src="/logo-dark.svg"
                  alt="NEP X Logo"
                  width={50}
                  height={66.64}
                  className="hidden dark:block"
                />
              </div>
              <span className="text-2xl font-semibold text-[var(--light-text)] dark:text-[var(--dark-text)]">
                NEP X{" "}
                <span className="bg-gradient-to-r from-[#1A7EF2] to-[#6366F1] bg-clip-text text-transparent">
                  Trading
                </span>
              </span>
            </Link>
            <p className="mt-4 text-sm leading-relaxed text-[var(--light-text-muted)] dark:text-[var(--dark-text-muted)]">
              Your all-in-one platform for NEPSE market insights, analysis, and
              trading education.
            </p>
            <div className="mt-6 flex items-center gap-4">
              {[Facebook, Twitter, Instagram, Youtube].map((Icon, idx) => (
                <Link
                  key={idx}
                  href="#"
                  className="text-[var(--light-text-muted)] dark:text-[var(--dark-text-muted)]"
                >
                  <Icon size={20} />
                </Link>
              ))}
            </div>
          </div>

          {/* Column 2: Quick Links */}
          <div>
            <h3 className="mb-4 text-base font-normal text-[var(--light-text)] dark:text-[var(--dark-text)]">
              Quick Links
            </h3>
            <ul className="flex flex-col gap-6">
              {quickLinks.map((link) => (
                <li key={link.label}>
                  <Link
                    href={link.href}
                    className="group relative text-sm text-[var(--light-text)] duration-300 dark:text-[var(--dark-text)]"
                  >
                    <span className="transition-all duration-300 group-hover:bg-gradient-to-r group-hover:from-[#1A7EF2] group-hover:to-[#6366F1] group-hover:bg-clip-text group-hover:text-transparent">
                      {link.label}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Column 3: Resources */}
          <div>
            <h3 className="mb-5 text-base font-normal text-[var(--light-text)] dark:text-[var(--dark-text)]">
              Resources
            </h3>
            <ul className="flex flex-col gap-6">
              {resourcesLinks.map((link) => (
                <li key={link.label}>
                  <Link
                    href={link.href}
                    className="group text-sm text-[var(--light-text)] dark:text-[var(--dark-text)]"
                  >
                    <span className="transition-all duration-300 group-hover:bg-gradient-to-r group-hover:from-[#1A7EF2] group-hover:to-[#6366F1] group-hover:bg-clip-text group-hover:text-transparent">
                      {link.label}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Column 4: Newsletter Signup */}
          <div>
            <h3 className="mb-5 text-base font-normal text-[var(--light-text)] dark:text-[var(--dark-text)]">
              Stay Updated
            </h3>
            <p className="text-sm text-[var(--light-text)] dark:text-[var(--dark-text)]">
              Subscribe to our newsletter for market updates and trading
              insights.
            </p>
            <form className="mt-4 flex h-12 w-full items-center rounded-lg border bg-[var(--light-surface)] text-[var(--light-text)] focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500 dark:border-slate-700 dark:bg-[var(--dark-border)] dark:text-[var(--dark-text)]">
              <input
                type="email"
                placeholder="Your Email"
                className="h-full flex-grow bg-transparent pl-4 text-sm focus:outline-none"
              />
              <button
                type="submit"
                className="flex h-full w-14 flex-shrink-0 items-center justify-center rounded-r-lg bg-[var(--color-company)] text-[var(--dark-text)] transition-colors hover:bg-blue-500"
                aria-label="Subscribe to newsletter"
              >
                <Mail size={20} />
              </button>
            </form>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 flex flex-col-reverse items-center justify-between gap-6 pt-5 sm:flex-row">
          <p className="text-sm text-[var(--light-text)] dark:text-[var(--dark-text)]">
            © {new Date().getFullYear()} Nep X. All rights reserved.
          </p>
          <div className="flex items-center gap-6">
            {["/privacy", "/terms", "/contact"].map((href, idx) => (
              <Link
                key={idx}
                href={href}
                className="text-sm text-[var(--light-text-muted)] dark:text-[var(--dark-text-muted)]"
              >
                {href === "/privacy"
                  ? "Privacy Policy"
                  : href === "/terms"
                    ? "Terms of Service"
                    : "Contact Us"}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
