import { CircleAlert } from "lucide-react";
import Top from "./Top";
import MarketCards from "./MarketCards";
import SummaryCard from "./SummaryCard";

const MarketHighlight = () => {
  return (
    <div className="mx-4 overflow-hidden rounded-2xl border border-[var(--dark-border)] pb-8 md:mx-10">
      {/* Header */}
      <div className="flex flex-col items-start justify-between gap-4 px-4 py-6 text-white md:flex-row md:items-center md:px-10">
        <h1 className="text-xl">
          Market <span className="text-[#6366F1]">Highlight</span>
        </h1>

        <div className="flex items-center gap-2 text-sm text-[#EDEDED]">
          <CircleAlert className="h-4 w-4" />
          <p>Last updated: Today, 3:00 PM</p>
        </div>
      </div>

      {/* Cards */}
      <div className="grid grid-cols-1 gap-2 px-4 pb-10 md:mt-4 md:grid-cols-4 md:gap-6 md:px-10">
        <MarketCards isProfit={true} />
        <MarketCards isProfit={false} />
        <MarketCards isProfit={true} />
        <MarketCards isProfit={false} />
      </div>

      <div className="col-span-12 grid grid-cols-12 gap-4 md:gap-0">
        {/* Top */}
        <div className="col-span-12 grid gap-10 px-4 md:col-span-8 md:ml-8 md:grid-cols-2 md:px-2">
          <Top isGainer={true} />
          <Top isGainer={false} />
        </div>
        {/* Summary Card */}
        <div className="col-span-12 px-4 md:col-span-4 md:px-10">
          <SummaryCard />
        </div>
      </div>
    </div>
  );
};

export default MarketHighlight;
