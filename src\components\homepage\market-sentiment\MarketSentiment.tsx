"use client";

import { RefreshCw } from "lucide-react";
import Link from "next/link";
import SearchButton from "./SearchButton";
import SentimentBar from "./SentimentBar";
import { sentimentData } from "@/app/constants";

const MarketSentiment = () => {
  return (
    <section className="mb-8 w-full rounded-b-xl bg-[#000000] p-4 sm:p-8">
      <div className="container mx-auto max-w-7xl">
        {/* Header */}
        <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
          <div>
            <h4 className="text-2xl font-semibold text-[var(--dark-text)] sm:text-3xl">
              Market{" "}
              <span className="bg-gradient-to-r from-[#1A7EF2] to-[#6366F1] bg-clip-text text-transparent">
                Sentiment Today
              </span>
            </h4>
            <p className="mt-1 text-[16px] text-[var(--dark-text-muted)]">
              See how investors feel about key stocks today.
            </p>
          </div>
          <div className="flex items-center gap-4">
            {/* Render the imported SearchButton component */}
            <SearchButton />

            <button className="flex items-center gap-2 text-sm text-[var(--color-company)]">
              <RefreshCw size={16} />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Sentiment Table Container */}
        <div className="mt-4 overflow-hidden rounded-lg border border-[var(--dark-border)]">
          {/* Table Header */}
          <div className="grid grid-cols-3 gap-4 border-b border-[var(--dark-border)] px-6 pt-8 pb-3">
            <h3 className="text-sm font-normal text-[var(--dark-text)]">
              Symbol
            </h3>
            <h3 className="text-center text-sm font-normal text-[var(--dark-text)]">
              Intraday Sentiment
            </h3>
            <h3 className="text-center text-sm font-normal text-[var(--dark-text)]">
              Daily Sentiment
            </h3>
          </div>

          {/* Table Body */}
          <div>
            {sentimentData.map((stock) => (
              <div
                key={stock.symbol}
                className="grid grid-cols-3 items-center gap-4 px-6 py-4"
              >
                {/* Symbol Column */}
                <div className="text-sm font-normal text-[var(--dark-text)]">
                  <span>{stock.symbol}</span>
                  <span className="ml-2 hidden text-[var(--dark-text-muted)] md:inline">
                    ({stock.name})
                  </span>
                </div>
                {/* Intraday Column */}
                <div>
                  <SentimentBar
                    value={stock.intraday}
                    color="--color-success"
                  />
                </div>
                {/* Daily Column */}
                <div>
                  <SentimentBar value={stock.daily} color="--color-error" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer Link */}
        <div className="group mt-4 flex justify-end">
          <Link
            href="#"
            className="transition-color text-sm font-medium text-[var(--dark-text)] duration-100 group-hover:bg-gradient-to-r group-hover:from-[#1A7EF2] group-hover:to-[#6366F1] group-hover:bg-clip-text group-hover:text-transparent"
          >
            View All
          </Link>
        </div>
      </div>
    </section>
  );
};

export default MarketSentiment;
