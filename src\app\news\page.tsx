import { Container } from "@/components/ui/common/container";
import { Search } from "lucide-react";
import React from "react";
import NewsFilterSection from "./newsFilterSection";

const NewsPage = () => {
  return (
    <div className="mt-25 min-h-screen py-3">
      {/* Top Heading Bar Section */}
      <Container>
        <div className="flex w-full items-center justify-between gap-2 px-8 py-3">
          <div>
            <h1 className="text-gradient-company text-2xl font-semibold">
              <span className="bg-gradient-to-b from-[#1a7ef2] to-[#6366f1] bg-clip-text text-transparent">
                News{" "}
              </span>
              <span className="text-[var(--dark-text)]">& Insights</span>
            </h1>
            <p className="text-base text-[var(--dark-text)]">
              Stay up-to-date with NEPSE market developments, official notices,
              and sentiment analysis
            </p>
          </div>

          {/* Post "ADD" & "Search" Button */}
          <div className="relative mb-2 flex w-fit items-center justify-between gap-2">
            <Search className="absolute top-1/2 left-3 h-5 w-5 -translate-y-1/2" />
            <input
              type="search"
              name="search"
              id="search"
              placeholder="Search Sectors or Keywords"
              className="w-full rounded-lg border border-[#6366F1] py-2 pr-3 pl-10 text-sm placeholder:text-sm placeholder:text-[var(--dark-text-muted)]"
            />
          </div>
        </div>
      </Container>

      {/* News Filter Section */}
      <NewsFilterSection />

      {/* Main & Side Bar Column Sections Here */}
      <div className="col-span-12 grid grid-cols-12">
        {/* Main Container Section */}
        <div className="col-span-12 lg:col-span-9"></div>

        {/* SideBar Container Section */}
        <div className="col-span-12 lg:col-span-3"></div>
      </div>
    </div>
  );
};

export default NewsPage;
