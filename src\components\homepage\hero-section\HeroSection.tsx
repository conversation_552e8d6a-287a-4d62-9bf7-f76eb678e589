"use client";

import {
  ArrowR<PERSON>,
  Users,
  TrendingUp,
  BarChart3,
  Briefcase,
} from "lucide-react";
import Image from "next/image";
import React from "react";
import TradingChart from "./TradingChart";
import { motion } from "framer-motion";

const HeroSection = () => {
  return (
    <div
      className="relative mb-8 w-full flex-col items-center justify-center overflow-hidden md:flex md:min-h-[585px] md:mb-1"
    >
      {/* Background Image */}
      <div className="hidden md:block">
        <div className="absolute top-[50px] left-[20px] z-0 flex h-[485px] w-[1376px] justify-between pr-[32px] pl-[32px]">
          <Image
            src="/hero-section/hero-section.png"
            alt="Background"
            width={1920}
            height={500}
            className="h-full w-full object-cover"
            priority
          />
        </div>
      </div>

      {/* Foreground Content */}
      <motion.div className="z-10 grid place-content-center px-4 text-white md:relative md:h-full md:grid-cols-2 md:px-14">
        {/* Left side content */}
        <div>
          {/* Header wrap */}
          <div className="mt-12 flex w-full flex-col items-center justify-center text-center md:min-w-[434px] md:items-start md:text-left">
            <h1 className="text-3xl font-semibold tracking-tight md:text-[32px] md:leading-[40px] md:tracking-normal">
              NEPSE Market Insights
              <br />
              Smart trading starts with{" "}
              <span className="text-[#6366F1]">better insights</span>
            </h1>
            <p className="pt-4 text-base font-normal">
              Nepal's smarter way to follow NEPSE — market insights, trader
              ideas & learning tools.
            </p>
          </div>

          {/* Main buttons */}
          <div className="grid w-fit grid-cols-2 gap-2 pt-8">
            <button className="flex items-center gap-3 rounded-lg px-2 py-1 text-sm transition-colors hover:cursor-pointer hover:bg-gray-800 md:px-6 md:py-3 md:text-xl">
              Explore Market <ArrowRight className="h-4 w-4" />
            </button>
            <button className="flex items-center gap-2 rounded-lg bg-blue-600 px-2 py-1 transition-colors hover:bg-blue-700 md:px-6 md:py-3">
              <Users className="h-4 w-4" />
              Join the Community
            </button>
          </div>

          {/* Feature buttons */}
          <div className="grid w-fit grid-cols-2 place-items-center-safe gap-4 pt-6 text-black md:grid-cols-3">
            <button className="flex items-center gap-2 rounded-full bg-white px-2 py-1 transition-colors">
              <TrendingUp className="h-4 w-4" />
              <span className="text-sm">Live Signals</span>
            </button>
            <button className="flex items-center gap-2 rounded-full bg-white px-2 py-1 transition-colors">
              <BarChart3 className="h-4 w-4" />
              <span className="text-sm">Market Analysis</span>
            </button>
            <button className="flex items-center gap-2 rounded-full bg-white px-2 py-1 transition-colors">
              <Briefcase className="h-4 w-4" />
              <span className="text-sm">Portfolio</span>
            </button>
          </div>
        </div>

        {/* Right side content (Chart) */}
        <div className="mt-12 flex w-full items-center justify-center md:ml-16">
          <TradingChart />
        </div>
      </motion.div>
    </div>
  );
};

export default HeroSection;