import Link from "next/link";
import {
  MessageSquare,
  ThumbsUp,
  Users,
  Share2,
  ArrowR<PERSON>,
} from "lucide-react";

const benefits = [
  {
    icon: MessageSquare,
    text: "Real-time trading discussions",
  },
  {
    icon: ThumbsUp,
    text: "Expert trade ideas and analysis",
  },
  {
    icon: Users,
    text: "Connect with successful traders",
  },
  {
    icon: Share2,
    text: "Share your own insights and strategies",
  },
];

const CommunityBenefits = () => {
  return (
    <div className="mb-5 flex h-full flex-col rounded-lg border-2 border-[var(--dark-border)] bg-[#1A1A1A4D] p-6 text-[#FFFFFF]">
      <h3 className="text-lg font-medium">Community Benefits</h3>
      <div className="mt-3 flex flex-grow flex-col gap-5">
        {benefits.map((benefit, index) => {
          const Icon = benefit.icon;
          return (
            <div key={index} className="flex items-center gap-2">
              <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-lg bg-[#0C63C840] text-[var(--color-company)]">
                <Icon size={16} />
              </div>
              <span className="text-sm">{benefit.text}</span>
            </div>
          );
        })}
      </div>
      <Link
        href="#"
        className="flex w-full items-center justify-center gap-3 rounded-lg border border-transparent bg-[var(--dark-border)] p-3 text-sm font-medium text-[var(--dark-text)] transition-colors duration-300 hover:border-white/20"
      >
        <span>Login</span>
        <ArrowRight size={16} />
      </Link>
    </div>
  );
};

export default CommunityBenefits;
