"use client";
import { PrimaryButtonProps, SecondaryButtonProps } from "@/types/buttons";
import { MessageSquare, Share2, ThumbsUp } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

// Primary Button with company bg
export const PrimaryButton = ({ label, icon }: PrimaryButtonProps) => {
  return (
    <div
      className={`flex items-center justify-center gap-2 rounded-sm bg-[var(--color-company)] px-2.5 py-2 text-base font-medium text-[var(--dark-text)]`}
    >
      <Image src={icon} alt={label} width={15} height={15} />
      {label}
    </div>
  );
};

// Secondary Button with company bg
export const SecondaryButton = ({
  label,
  icon,
  className,
}: SecondaryButtonProps) => {
  return (
    <div
      className={`flex items-center justify-center gap-2 rounded-sm bg-[#1F2937CC] px-2.5 py-2 text-base font-medium text-[var(--dark-text)] ${className}`}
    >
      {icon && <Image src={icon} alt={label} width={15} height={15} />}

      {label}
    </div>
  );
};

// Like Button
export const LikeButton = () => {
  const [liked, setLiked] = useState<boolean>(false);
  return (
    <button
      type="button"
      onClick={() => setLiked(!liked)}
      className={`flex items-center gap-1 text-[var(--dark-text-muted)]`}
    >
      {liked ? (
        <ThumbsUp className="h-5 w-5" fill="var(--color-company)" />
      ) : (
        <ThumbsUp className="h-5 w-5" />
      )}

      <span className="text-sm">48</span>
    </button>
  );
};

// Comment Button
export const CommentButton = () => {
  return (
    <div className="flex items-center gap-1 text-[var(--dark-text-muted)]">
      <MessageSquare className="h-5 w-5" /> <span className="text-sm">48</span>
    </div>
  );
};

// Share Button
export const ShareButton = () => {
  const [shared, setShared] = useState<boolean>(false);
  return (
    <button
      type="button"
      onClick={() => setShared(!shared)}
      className={`flex items-center gap-1 text-[var(--dark-text-muted)]`}
    >
      {shared ? (
        <Share2 className="h-5 w-5 text-[var(--color-company)]" />
      ) : (
        <Share2 className="h-5 w-5" />
      )}

      <span className="text-sm">48</span>
    </button>
  );
};
