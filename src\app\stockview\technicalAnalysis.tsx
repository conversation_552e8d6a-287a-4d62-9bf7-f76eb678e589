"use client";
import React from "react";
import { motion } from "motion/react";
import { Minus, TrendingUp } from "lucide-react";
import { TechnicalAnalysisProps } from "@/types/stockdetails";

const TechnicalAnalysis = () => {
  const technicalAnalysisData: TechnicalAnalysisProps[] = [
    { title: "RSI", value: 68, icon: <Minus /> },
    { title: "MACD", value: 68, icon: <TrendingUp color="#16c784" /> },
    {
      title: "Moving Averages",
      value: 68,
      icon: <TrendingUp color="#16c784" />,
    },
  ];
  return (
    <div className="h-auto w-full rounded-lg border border-[var(--dark-border)] p-5">
      {/* Animated Container */}
      <div>
        <div className="relative flex h-[162px] w-full items-center justify-between">
          <div>
            <span className="text-xs font-normal text-[var(--dark-text-muted)]">
              Technical Score
            </span>
            <h3 className="text-xl font-medium text-[var(--color-success)]">
              Strong Buy
            </h3>
          </div>

          <motion.div
            animate={{
              translateY: [-17, 10, -17],
            }}
            transition={{
              duration: 2,
              ease: "easeInOut",
              repeat: Infinity,
            }}
            style={{ background: "var(--dark-bg)" }}
            className="flex h-[122px] w-[122px] items-center justify-center overflow-hidden rounded-full border border-[var(--dark-border)] bg-amber-800"
          >
            <span className="text-xs font-normal text-[var(--dark-text-muted)]">
              Source: NEPSE AI
            </span>
          </motion.div>
        </div>
      </div>

      {/* Dark Panel For RSI, MACD, And Moving Averages */}
      <div className="grid grid-cols-1 gap-2 md:grid-cols-3">
        {technicalAnalysisData.map((item, idx) => (
          <TechnicalDarkPanel key={idx} {...item} />
        ))}
      </div>
    </div>
  );
};

export default TechnicalAnalysis;

export const TechnicalDarkPanel = ({
  title,
  value,
  icon,
}: TechnicalAnalysisProps) => {
  return (
    <div className="w-full rounded-lg bg-[#000000] p-3 text-[var(--dark-text)]">
      <div className="flex items-center justify-between">
        <p className="text-xs">{title}</p>
        {/* <Minus className="h-4 w-4" /> */}
        {icon}
      </div>
      <p className="mt-2 text-base font-medium">{value}</p>
    </div>
  );
};
