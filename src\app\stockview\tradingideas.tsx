import { TradingIdeaProps } from "@/types/stockdetails";
import Image from "next/image";
import { FreeMode, Mousewheel } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/free-mode";

const TradingIdeas = () => {
  const tradingIdeasData = [
    {
      title: "NABIL: Breakout Above Rs. 1300 in...",
      content:
        "Volume building steadily. A close above Rs. 1300 could trigger momentum toward Rs. 1350.",
      avtar: "/stockview/user2.png",
      name: "<PERSON><PERSON>l",
      likesCount: 56,
    },
    {
      title: "NABIL: Bullish Pennant Forming",
      content:
        "Price consolidating within tight range. Watch for breakout with volume confirmation.",
      avtar: "/stockview/user3.png",
      name: "<PERSON><PERSON><PERSON>",
      likesCount: 56,
    },
    {
      title: "NABIL: MACD Crossover Confirmed",
      content:
        "MACD line crosses signal line upward. Momentum shift suggests Rs. 1325 target.",
      avtar: "/stockview/user4.png",
      name: "<PERSON><PERSON><PERSON>",
      likesCount: 45,
    },
    {
      title: "NABIL: Bearish Divergence on RSI",
      content:
        "RSI shows lower highs while price climbs. Possible pullback to Rs. 1260 support.",
      avtar: "/stockview/user2.png",
      name: "Kiran Adhikari",
      likesCount: 32,
    },
    {
      title: "NABIL: Bollinger Band Squeeze",
      content:
        "Volatility compression suggests breakout imminent. Direction unclear—watch volume.",
      avtar: "/stockview/user1.jpg",
      name: "Priya Lama",
      likesCount: 59,
    },
  ];
  return (
    <Swiper
      modules={[FreeMode, Mousewheel]}
      spaceBetween={14}
      slidesPerView="auto"
      freeMode={true}
      mousewheel={{
        forceToAxis: true,
        sensitivity: 1,
        releaseOnEdges: true,
      }}
      grabCursor={true}
      className="related-news-swiper"
    >
      {tradingIdeasData.map((item, idx) => (
        <SwiperSlide key={idx} style={{ width: "auto" }}>
          <TradingIdeasCard
            avatar={item.avtar}
            name={item.name}
            title={item.title}
            content={item.content}
            likesCount={item.likesCount}
          />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default TradingIdeas;

export const TradingIdeasCard = ({
  title,
  content,
  avatar,
  name,
  likesCount,
}: TradingIdeaProps) => {
  return (
    <div className="max-w-[322px] rounded-md border border-[var(--dark-border)] bg-[var(--dark-surface)] p-3">
      {/* Dark Panel */}
      <div className="rounded-md bg-[#000000] px-14 py-14 text-center">
        <span className="text-xs text-[var(--dark-text-muted)]">
          Mini chart preview
        </span>
      </div>

      {/* Content Section from here */}
      <h3 className="mt-2 line-clamp-1 text-base font-normal text-[var(--dark-text)]">
        {title}
      </h3>
      <p className="mt-1 line-clamp-2 text-xs text-[var(--dark-text-muted)]">
        {content}
      </p>

      <footer className="mt-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 overflow-hidden rounded-full">
            <Image
              src={avatar}
              alt={name}
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
          <h5 className="text-xs text-[var(--dark-text-muted)]">{name}</h5>
        </div>
        <p className="text-xs text-[var(--dark-text-muted)]">
          {likesCount} Likes
        </p>
      </footer>
    </div>
  );
};
