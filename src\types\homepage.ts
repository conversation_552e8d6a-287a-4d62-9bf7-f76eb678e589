
// Market Highlights Top Card
export interface Item {
  name: string;
  fullName: string;
  change: string;
  price: string;
}

export interface TopData {
  id: number;
  name: string,
  fullName: string,
  change: string,
  price: string,
}

export interface NewsData {
  id: number;
  category: string;
  time: string;
  title: string;
  description: string;
  source: string;
  link: string;
}

export interface SummaryItem {
  label: string;
  value: string;
  color?: "green" | "red";
}

export interface CourseCardProps {
  id: number;
  imageUrl: string;
  title: string;
  chanel: string;
  authorLogoUrl: string;
  lessons: number;
  students: number;
  rating: number;
  loginLink: string;
  enrollLink: string;
  priceTag: string;
  level: string;
};

export interface PostCardProps {
  authorInitials: string;
  authorName: string;
  authorStatus?: string;
  timestamp: string;
  content: string;
  likes: number;
  comments: number;
  shares: number;
  discussionLink: string;
};

export interface SentimentBarProps {
  value: number;
  color: '--color-success' | '--color-error';
};

export interface SentimentData {
  symbol: string;
  name: string;
  intraday: number;
  daily: number;
}