import {
  ShrinkableAddButton,
  ShrinkableSearchButton,
} from "@/components/ui/common/AnimatedButton";
import { Container } from "@/components/ui/common/container";
import {
  BottomMultiSelectButton,
  TopMultiSelectButton,
} from "./multiSelectorButtons";
import CommunityPosts from "./communityPosts";
import CommunitySideBar from "./communitysidebar";
// import CommunityPost from "@/components/communitypost";

const CommunityHub = () => {
  return (
    <Container className="mt-25 min-h-screen px-2 py-3 sm:px-8">
      {/* Top Heading Bar Section */}
      <div className="flex w-full flex-row items-center justify-between gap-2 px-2">
        <h1 className="text-gradient-company text-center text-2xl font-semibold">
          <span className="bg-gradient-to-b from-[#1a7ef2] to-[#6366f1] bg-clip-text text-transparent">
            Community{" "}
          </span>
          <span className="text-[var(--dark-text)]">Hub</span>
        </h1>
        {/* Post "ADD" & "Search" Button */}
        <div className="mb-2 flex items-center justify-between gap-2">
          <ShrinkableSearchButton />
          <ShrinkableAddButton />
        </div>
      </div>

      {/* Main & Sidebar Column Section */}
      <div className="col-span-12 grid grid-cols-12 gap-4">
        {/* Main Column Section - 9 columns */}
        <main className="col-span-12 lg:col-span-9">
          <header className="w-full rounded-lg border border-[var(--dark-border)]">
            {/* Top Container */}
            <div className="px-4 py-2">
              {/* Buttons Selections Container  */}
              <TopMultiSelectButton />
              <BottomMultiSelectButton />
            </div>
          </header>

          <main className="py-4 text-[var(--dark-text)]">
            <CommunityPosts />
            {/* <CommunityPost /> */}
          </main>
        </main>

        {/* Sidebar Column - 2 columns */}
        <aside className="col-span-12 lg:col-span-3">
          <div className="sticky top-22 px-4">
            <CommunitySideBar />
          </div>
        </aside>
      </div>
    </Container>
  );
};

export default CommunityHub;
