import { CommunityPostProps, TopHastagsProps } from "@/types/community";
import {
  CourseCardProps,
  NewsData,
  TopData,
  PostCardProps,
  SentimentData,
  SummaryItem,
} from "@/types/homepage";

export const topGainersData: TopData[] = [
  {
    id: 1,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 2,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 3,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 4,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 5,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 6,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 7,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 8,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 9,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
];

export const topLosersData: TopData[] = [
  {
    id: 1,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 2,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 3,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 4,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 5,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 6,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 7,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 8,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
  {
    id: 9,
    name: "NABIL",
    fullName: "Nabil Bank",
    change: "+4.5%",
    price: "Rs. 1,245.00",
  },
];

export const newsData: NewsData[] = [
  {
    id: 1,
    category: "Top Gainers",
    time: "3 hours ago",
    title: "SEBON Approves New Broker License Applications",
    description:
      "The Securities Board of Nepal has approved applications for 10 new broker licenses, expanding market access.",
    source: "NEPSE Official",
    link: "#",
  },
  {
    id: 2,
    category: "Earnings",
    time: "3 hours ago",
    title: "Commercial Banks Report 18% Average Growth in Q3",
    description:
      "Nepal's commercial banks have reported an average of 18% growth in third-quarter profits compared to the same ",
    source: "NEPSE Official",
    link: "#",
  },
  {
    id: 3,
    category: "Industry",
    time: "Yesterday",
    title: "Hydropower Sector Sees Foreign Investment Surge",
    description:
      "The Securities Board of Nepal has approved applications for 10 new broker licenses, expanding market access.",
    source: "Energy Nepal",
    link: "#",
  },
  {
    id: 4,
    category: "Industry",
    time: "Yesterday",
    title: "NEPSE Crosses 2,200 Mark Amid Renewed Investor Confidence",
    description:
      "The Nepal Stock Exchange (NEPSE) surged past the 2,200-point mark today, reflecting increased investor optimism fueled by stable political signals ",
    source: "Energy Nepal",
    link: "#",
  },
  {
    id: 5,
    category: "Industry",
    time: "Yesterday",
    title:
      "Commercial Banks Report Strong Q4 Earnings, Boosting Market Sentiment",
    description:
      "Nepal’s leading commercial banks have posted better-than-expected fourth-quarter earnings, driven by increased loan disbursement.",
    source: "Energy Nepal",
    link: "#",
  },
];

export const summaryData: SummaryItem[] = [
  { label: "Turnover", value: "Rs. 4.2 Billion" },
  { label: "Volume", value: "12.4 Million" },
  { label: "Trades", value: "89,245" },
  { label: "Traded Scrips", value: "228" },
  { label: "Advances", value: "146", color: "green" },
  { label: "Declines", value: "74", color: "red" },
  { label: "Unchanged", value: "8" },
];

export const coursesData: CourseCardProps[] = [
  {
    id: 1,
    imageUrl: "/courses/course1.jpg",
    title: "NEPSE Trading Fundamentals",
    chanel: "NEP X Trading",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 12,
    students: 1245,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Free",
    level: "Beginner",
  },
  {
    id: 2,
    imageUrl: "/courses/course2.jpg",
    title: "Advanced Trading Strategies",
    chanel: "ProTrader Hub",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 25,
    students: 1150,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Paid",
    level: "Experienced",
  },
  {
    id: 3,
    imageUrl: "/courses/course3.jpg",
    title: "Risk Management & Portfolio",
    chanel: "Nepal Investment School",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 20,
    students: 900,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Paid",
    level: "Intermediate",
  },
  {
    id: 4,
    imageUrl: "/courses/course4.jpg",
    title: "Understanding Financial Statements",
    chanel: "NEP X Trading",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 10,
    students: 1900,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Free",
    level: "Beginner",
  },
  {
    id: 5,
    imageUrl: "/courses/course5.jpg",
    title: "Technical Analysis Fundamentals",
    chanel: "NEP X Trading",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 12,
    students: 1245,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Free",
    level: "Beginner",
  },
  {
    id: 6,
    imageUrl: "/courses/course5.jpg",
    title: "Stock Market Basics: Getting Started",
    chanel: "NEP X Trading",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 12,
    students: 1245,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Free",
    level: "Beginner",
  },
  {
    id: 7,
    imageUrl: "/courses/course5.jpg",
    title: "Stock Market Basics: Getting Started",
    chanel: "NEP X Trading",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 12,
    students: 1245,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Free",
    level: "Beginner",
  },
  {
    id: 8,
    imageUrl: "/courses/course5.jpg",
    title: "Stock Market Basics: Getting Started",
    chanel: "NEP X Trading",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 12,
    students: 1245,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Free",
    level: "Beginner",
  },
  {
    id: 9,
    imageUrl: "/courses/course5.jpg",
    title: "Stock Market Basics: Getting Started",
    chanel: "NEP X Trading",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 12,
    students: 1245,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Free",
    level: "Beginner",
  },
  {
    id: 10,
    imageUrl: "/courses/course5.jpg",
    title: "Stock Market Basics: Getting Started",
    chanel: "NEP X Trading",
    authorLogoUrl: "/logo-dark.svg",
    lessons: 12,
    students: 1245,
    rating: 4.8,
    loginLink: "#",
    enrollLink: "#",
    priceTag: "Free",
    level: "Beginner",
  },
];

export const roadmapSteps = [
  {
    title: "Learn the Basics",
    description: "Understand NEPSE, stock types, and basic market terminology",
  },
  {
    title: "Master Technical Analysis",
    description:
      "Learn to read charts, identify patterns, and use indicators effectively.",
  },
  {
    title: "Understand Fundamental Analysis",
    description:
      "Evaluate company financials, industry trends, and economic factors.",
  },
  {
    title: "Develop a Trading Strategy",
    description:
      "Define your risk tolerance, set goals, and create your own trading rules.",
  },
];

export const posts: PostCardProps[] = [
  {
    authorInitials: "SL",
    authorName: "Saurav Lama",
    authorStatus: "Verified Analyst",
    timestamp: "2 hours ago",
    content:
      "NABIL showing strong support at 1200 with increasing volume. Watch for a potential breakout above 1250 in the coming sessions.",
    likes: 48,
    comments: 12,
    shares: 5,
    discussionLink: "#",
  },
  {
    authorInitials: "RR",
    authorName: "Raju Raja Ram",
    timestamp: "Yesterday",
    content:
      "Banking sector looks oversold at current levels. The recent policy changes should provide positive momentum in the next few weeks.",
    likes: 36,
    comments: 8,
    shares: 2,
    discussionLink: "#",
  },
  {
    authorInitials: "RR",
    authorName: "Raju Raja Ram",
    timestamp: "Yesterday",
    content:
      "Banking sector looks oversold at current levels. The recent policy changes should provide positive momentum in the next few weeks.",
    likes: 36,
    comments: 8,
    shares: 2,
    discussionLink: "#",
  },
  {
    authorInitials: "RR",
    authorName: "Raju Raja Ram",
    timestamp: "Yesterday",
    content:
      "Banking sector looks oversold at current levels. The recent policy changes should provide positive momentum in the next few weeks.",
    likes: 36,
    comments: 8,
    shares: 2,
    discussionLink: "#",
  },
];

export const sentimentData: SentimentData[] = [
  { symbol: "NABIL", name: "Nabil Bank Limited", intraday: 68, daily: 72 },
  {
    symbol: "NLIC",
    name: "Nepal Life Insurance Co. Ltd.",
    intraday: 75,
    daily: 80,
  },
  { symbol: "NICA", name: "NIC Asia Bank Ltd.", intraday: 64, daily: 58 },
  { symbol: "GBIME", name: "Global IME Bank Ltd.", intraday: 45, daily: 52 },
  { symbol: "SANIMA", name: "Sanima Bank Ltd.", intraday: 38, daily: 42 },
  {
    symbol: "BPCL",
    name: "Butwal Power Company Limited",
    intraday: 55,
    daily: 60,
  },
];

export const communityPosts: CommunityPostProps[] = [
  {
    id: 3,
    title: "NEPSE Testing Resistance at 2050 – What's Next?",
    content:
      "The index is approaching a major resistance zone around 2050. If we get a strong close above this level with volume confirmation, we could see a short-term rally. However, rejection could trigger a pullback to the 2000 level.",
    chart_image: "/community/postCoverImage.jpg",
    tags: "idea",
    hastags: ["#NEPSE", "#ResistanceLevel", "#ChartAnalysis"],
    user: {
      id: 11,
      firstname: "Kiran",
      lastname: "Lama",
      user_image: "/stockview/user3.png",
      is_verified: true,
    },
    created_at: "2024-04-02T09:15:00Z",
  },
  {
    id: 4,
    title: "Question on RSI Overbought Condition",
    content:
      "I noticed that SHIVM is showing RSI levels above 70, but the stock keeps climbing. Isn’t this supposed to be overbought? Why is the price still going up?",
    chart_image: null,
    tags: "question",
    hastags: ["#RSI", "#Overbought", "#Confused"],
    user: {
      id: 15,
      firstname: "Sneha",
      lastname: "Karki",
      user_image: "/stockview/user4.png",
      is_verified: false,
    },
    created_at: "2024-04-03T14:20:00Z",
  },
  {
    id: 5,
    title: "Dividend Announcement Impact on Price",
    content:
      "NRIC declared a 15% dividend yesterday, and we saw a gap-up at the open. Historically, do dividend announcements lead to sustainable price moves, or are they usually short-lived?",
    chart_image: "/community/postCoverImage.jpg",
    tags: "articles",
    hastags: ["#Dividend", "#NRIC", "#PriceAction"],
    user: {
      id: 18,
      firstname: "Manish",
      lastname: "Shrestha",
      user_image: "/stockview/user1.jpg",
      is_verified: true,
    },
    created_at: "2024-04-04T11:45:00Z",
  },
  {
    id: 6,
    title: "Bullish Flag Forming in Hydropower Stocks",
    content:
      "Technical structure across hydropower stocks like AKPL and RHPL are showing bullish flag formations on the 4H chart. Volume is drying up during consolidation—watching for a breakout soon.",
    chart_image: "/community/postCoverImage.jpg",
    tags: "experiences",
    hastags: ["#Hydropower", "#BullishFlag", "#ChartSetup"],
    user: {
      id: 19,
      firstname: "Saurav",
      lastname: "Gurung",
      user_image: "/stockview/user2.png",
      is_verified: true,
    },
    created_at: "2024-04-06T13:00:00Z",
  },
  {
    id: 7,
    title: "Is it too late to enter after breakout?",
    content:
      "I've been watching NLIC for a few days. It just broke out of a key resistance with strong volume, but I missed the entry. Should I wait for a pullback or jump in now?",
    chart_image: null,
    tags: "question",
    hastags: ["#Breakout", "#EntryPoint", "#NLIC"],
    user: {
      id: 20,
      firstname: "Deepika",
      lastname: "Maharjan",
      user_image: "/stockview/user1.jpg",
      is_verified: false,
    },
    created_at: "2024-04-07T16:25:00Z",
  },
];

export const topContributors = [
  { image: "/stockview/user4.png", name: "Rajesh Sharma", contributions: 142 },
  { image: "/stockview/user3.png", name: "Priya Thapa", contributions: 118 },
  { image: "/stockview/user2.png", name: "Suman Adhikari", contributions: 97 },
  { image: "/stockview/user4.png", name: "Anita Gurung", contributions: 86 },
];

export const topHastagsData: TopHastagsProps[] = [
  { hashtag: "Banking", count: 243 },
  { hashtag: "RSI", count: 186 },
  { hashtag: "TechnicalAnalysis", count: 154 },
  { hashtag: "Beginner", count: 132 },
  { hashtag: "MACD", count: 118 },
  { hashtag: "Dividend", count: 97 },
];

export const ctaCategoryData: string[] = [
  "Financial News",
  "Government Policy",
  "SENS Notice",
  "Company Updates",
];

export const ctaIndustriesData = [
  { label: "Banking" },
  { label: "Hydro" },
  { label: "Insurance " },
  { label: "Others" },
];
