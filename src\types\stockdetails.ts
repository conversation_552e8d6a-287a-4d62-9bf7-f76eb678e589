export interface CommunityPostsProps {
  avatar: string;
  name: string;
  timestamp: string;
  content: string;
}

export interface RelatedNewsProps {
  avatar: string;
  name: string;
  timestamp: string;
  content: string;
  tags: string;
}

export interface TradingIdeaProps {
  avatar: string;
  name: string;
  content: string;
  title: string;
  likesCount: number;
}

export interface TechnicalAnalysisProps {
  title: string;
  value: number;
  icon: React.ReactNode;
}
