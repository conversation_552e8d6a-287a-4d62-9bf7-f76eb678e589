"use client";

import { CourseCardProps } from "@/types/homepage";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Star, Users } from "lucide-react";
import { motion, Variants } from "motion/react";
import Image from "next/image";
import Link from "next/link";

const cardVariants: Variants = {
  initial: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    boxShadow: "0 0 12px 0px rgba(255, 255, 255, 0.0)",
    transition: { duration: 0.3, ease: "easeInOut" },
  },
  hover: {
    backgroundColor: "var(--dark-bg, #10131c)",
    boxShadow: "0 0 8px 2px rgba(255, 255, 255, 0.2)",
    transition: { duration: 0.3, ease: "easeInOut" },
  },
};

// Variants for the tags to fade and slide in/out
const tagVariants: Variants = {
  initial: {
    opacity: 0,
    y: -10, // Start slightly above
    transition: { duration: 0.2, ease: "easeOut" },
  },
  hover: {
    opacity: 1,
    y: 0, // Animate to original position
    transition: { duration: 0.2, ease: "easeIn" },
  },
};

const CourseCard = ({
  imageUrl,
  title,
  chanel,
  authorLogoUrl,
  lessons,
  students,
  rating,
  loginLink,
  enrollLink,
  priceTag,
  level,
}: CourseCardProps) => {
  return (
    // Main card container
    <motion.div
      className="mx-8 mt-10 mb-3 flex w-full max-w-sm flex-col overflow-hidden rounded-lg border-[var(--dark-border)] p-4 pb-1 text-white"
      variants={cardVariants}
      initial="initial"
      whileHover="hover"
    >
      {/* Course Thumbnail Image with Hover Tags */}
      <motion.div className="relative h-48 w-full">
        <Image
          src={imageUrl}
          alt={title}
          layout="fill"
          objectFit="cover"
          className="rounded-lg"
        />

        {/* Free Tag (top-left) */}
        {priceTag && (
          <motion.div
            className="absolute top-4 left-4 rounded bg-blue-600 px-3 py-1 text-xs font-semibold text-white"
            variants={tagVariants} // Apply the animation variants
          >
            {priceTag}
          </motion.div>
        )}

        {/* Level Tag (top-right) */}
        {level && (
          <motion.div
            className="absolute top-4 right-4 rounded bg-black/70 px-3 py-1 text-xs font-semibold text-white backdrop-blur-sm"
            variants={tagVariants} // Apply the animation variants
          >
            {level}
          </motion.div>
        )}
      </motion.div>

      {/* Content Section */}
      <div className="flex flex-grow flex-col px-1 py-5">
        {/* Course Title */}
        <h3 className="line-clamp-1 text-lg font-normal text-white">{title}</h3>

        {/* Author Info */}
        <div className="mt-3 flex items-center">
          <div className="relative h-8 w-8 overflow-hidden rounded-full">
            <Image
              src={authorLogoUrl}
              alt={chanel}
              width={20}
              height={20}
              className="rounded-full"
              objectFit="cover"
            />
          </div>
          <span className="text-sm font-medium text-[var(--dark-text-muted)]">
            {chanel}
          </span>
        </div>

        {/* Course Metadata (Lessons, Students) */}
        <div className="mt-4 flex flex-wrap items-center gap-x-5 gap-y-2 text-sm text-[var(--dark-text-muted)]">
          <div className="flex items-center gap-1.5">
            <BookOpen size={16} className="text-white" />
            <span>{lessons} Lessons</span>
          </div>
          <div className="flex items-center gap-1.5">
            <Users size={16} className="text-white" />
            <span>{students.toLocaleString()} students</span>
          </div>
        </div>

        {/* Star Rating */}
        <div className="mt-3 flex items-center gap-1.5 text-[var(--dark-text-muted)]">
          <Star size={16} className="fill-[#F6C244] text-[#F6C244]" />
          <span className="text-sm font-semibold">{rating}</span>
        </div>

        {/* Footer: Login and Enroll Now */}
        <div className="mt-3 flex items-center justify-between pt-2">
          <Link
            href={loginLink}
            className="group flex items-center gap-2 rounded-sm bg-transparent transition-colors duration-400 hover:bg-[var(--dark-border)]"
          >
            <span className="flex items-center gap-2 p-2 text-white group-hover:bg-gradient-to-r group-hover:from-[#1A7EF2] group-hover:to-[#6366F1] group-hover:bg-clip-text group-hover:text-transparent">
              <span>Login</span>
              <ArrowRight
                size={16}
                className="text-white transition-colors duration-300 group-hover:text-[#1A7EF2]"
              />
            </span>
          </Link>
          <Link href={enrollLink}>
            <span className="flex items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-2.5 text-sm font-semibold text-white transition-colors hover:bg-blue-500">
              <Users size={16} />
              <span>Enroll Now</span>
            </span>
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

export default CourseCard;
