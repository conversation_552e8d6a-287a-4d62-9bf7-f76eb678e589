import { Users } from "lucide-react";
import CommunityPostCard from "./CommunityPostCard";
import CommunityBenefits from "./CommunityBenefits";
import { posts } from "@/app/constants";

const TradingCommunity = () => {
  return (
    <div className="bg-[#FFFFFF03] px-5 py-10">
      <div className="container mx-auto max-w-7xl">
        {/* Header Section */}
        <div className="mb-10 flex flex-col items-start justify-between gap-4 md:flex-row md:items-end">
          <div>
            <h2 className="text-2xl font-semibold text-[var(--dark-text)] sm:text-2xl">
              Join Our{" "}
              <span className="bg-gradient-to-r from-[#1A7EF2] to-[#6366F1] bg-clip-text text-transparent">
                Trading Community
              </span>
            </h2>
            <p className="mt-2 text-sm text-[var(--dark-text-muted)]">
              Connect with fellow traders, share insights, and learn from
              Experts
            </p>
          </div>
          <div className="flex-shrink-0 rounded-full bg-[#0C63C833] px-4 py-2 text-sm font-medium">
            <span className="flex items-center gap-2 text-[var(--dark-text)]">
              <Users size={20} className="text-[var(--color-company)]" />
              4,500+ active members
            </span>
          </div>
        </div>

        {/* Main Grid: Posts and Benefits Sidebar */}
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Posts Column */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:col-span-2">
            {posts.map((post, index) => (
              <CommunityPostCard key={index} {...post} />
            ))}
          </div>

          {/* Benefits Sidebar */}
          <div className="lg:col-span-1">
            <CommunityBenefits />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradingCommunity;
