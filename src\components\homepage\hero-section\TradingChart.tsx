"use client";

import React, { useState } from "react";
import Image from "next/image";
import { motion } from "framer-motion";

const TradingChart = () => {
  const [showPopup, setShowPopup] = useState(false);

  return (
    <div className="relative w-full max-w-[500px] px-4 md:mt-16 md:ml-5 md:px-0">
      <div
        className={`relative z-10 inline-block w-full rounded-lg border border-[var(--dark-border)] ${
          showPopup ? "shadow-[1.5px_-6px_5px_0px_rgba(255,255,255,0.1)]" : ""
        }`}
        onMouseEnter={() => setShowPopup(true)}
        onMouseLeave={() => setShowPopup(false)}
      >
        <Image
          src="/hero-section/trading-chart.png"
          alt="Trading Chart"
          width={500}
          height={600}
          className="w-full rounded-lg object-contain"
          priority
        />
      </div>

      {/* Popup */}
      {showPopup && (
        <motion.div
          className="absolute inset-0 flex h-28 w-full flex-col justify-center rounded-lg border border-[var(--dark-border)] px-6"
          style={{
            background: "var(--dark-bg)",
          }}
          initial={{ opacity: 0, y: 0 }}
          animate={{ opacity: 1, y: -100 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
        >
          <div className="flex w-full items-start justify-between">
            <div>
              <div className="mb-1 text-[12.19px] leading-[16.25px] font-normal text-gray-300">
                NEPSE Index
              </div>
              <div className="text-[20.31px] leading-[28.43px] font-medium text-white">
                2,147.63
              </div>
            </div>
            <div className="mt-2 text-[14.22px] leading-[24.37px] font-normal text-green-400">
              +1.27%
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default TradingChart;
