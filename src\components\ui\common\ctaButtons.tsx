"use client";
import { BookmarkIcon, Refresh<PERSON>c<PERSON>, Star } from "lucide-react";
import { motion } from "motion/react";
import { useState } from "react";

export const CtaCategory = ({ label }: { label: string }) => {
  return (
    <div className="cursor-pointer rounded-[20px] bg-[var(--dark-border)] px-2.5 py-1 hover:bg-[#2182F299]">
      <p className="text-xs text-[var(--dark-text)]">{label}</p>
    </div>
  );
};

export const CtaIndustry = ({ label }: { label: string }) => {
  return (
    <div className="h-fit w-fit cursor-pointer rounded-[20px] bg-[var(--dark-border)] px-2.5 py-1 hover:bg-[#37DC7499]">
      <p className="text-xs text-[var(--dark-text)]">{label}</p>
    </div>
  );
};

export const CtaBullish = () => {
  return (
    <div className="h-fit w-fit cursor-pointer rounded-[20px] bg-[var(--dark-border)] px-2.5 py-1 hover:bg-[var(--color-success)]">
      <p className="text-xs text-[var(--dark-text)]">🟢 Bullish</p>
    </div>
  );
};

export const CtaBearish = () => {
  return (
    <div className="h-fit w-fit cursor-pointer rounded-[20px] bg-[var(--dark-border)] px-2.5 py-1 hover:bg-[var(--color-error)]">
      <p className="text-xs text-[var(--dark-text)]">🔴 Bearish</p>
    </div>
  );
};

export const CtaNeutral = () => {
  return (
    <div className="h-fit w-fit cursor-pointer rounded-[20px] bg-[var(--dark-border)] px-2.5 py-1 hover:bg-[var(--dark-text-muted)]">
      <p className="text-xs text-[var(--dark-text)]">⚪️ Neutral</p>
    </div>
  );
};

export const Reset = () => {
  const [isReset, setIsReset] = useState(false);
  const resetButtonHandler = () => {
    setIsReset(!isReset);
  };
  return (
    <button className="flex items-center gap-1" onClick={resetButtonHandler}>
      <RefreshCcw
        className={`h-5 w-5 transform transition-transform duration-500 ease-in-out ${
          isReset ? "rotate-180" : "rotate-0"
        }`}
      />
      <p className="text-base text-[#D9D9D9]">Reset</p>
    </button>
  );
};

export const Bookmark = () => {
  const [isBookmarked, setIsBookmarked] = useState(false);
  const bookMarkButtonHandler = () => {
    setIsBookmarked(!isBookmarked);
    console.log(isBookmarked);
  };
  return (
    <button
      onClick={() => bookMarkButtonHandler()}
      className="flex items-center gap-1"
    >
      <Star
        className={`h-5 w-5 ${isBookmarked ? "text-[var(--color-warning)]" : "text-[#D9D9D9]"}`}
      />
      <p
        className={`cursor-pointer text-base ${isBookmarked ? "text-[var(--color-warning)]" : "text-[#D9D9D9]"}`}
      >
        Bookmark
      </p>
    </button>
  );
};
