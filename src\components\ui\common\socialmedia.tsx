import { SocialMediaLink } from "@/lib/links";
import { Upload } from "lucide-react";
import Link from "next/link";
import { FaFacebookF, FaTelegram, FaTwitter } from "react-icons/fa6";

// FaceBook Icon Button
export const FaceBookIcon = () => {
  return (
    <Link href={SocialMediaLink.facebook} target="_blank">
      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-[var(--dark-border)]">
        <FaFacebookF className="text-[#2563EB]" />
      </div>
    </Link>
  );
};

// Twitter Icon Button
export const TwitterIcon = () => {
  return (
    <Link href={SocialMediaLink.twitter} target="_blank">
      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-[var(--dark-border)]">
        <FaTwitter className="text-[#60A5FA]" />
      </div>
    </Link>
  );
};

// Telegram Icon Button
export const TelegramIcon = () => {
  return (
    <Link href={SocialMediaLink.telegram} target="_blank">
      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-[var(--dark-border)]">
        <FaTelegram className="text-[#2563EB]" />
      </div>
    </Link>
  );
};

// Share Icon Button
export const ShareIcon = () => {
  return (
    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-[var(--dark-border)]">
      <Upload className="text-[#2563EB]" />
    </div>
  );
};
