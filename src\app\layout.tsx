import type { Metada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import NavBar from "@/components/header/navbar";
import { ThemeProvider } from "@/components/ThemeProvider";
import Footer from "@/components/footer/Footer";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["600", "500", "400"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${poppins.variable} bg-[var(--light-bg)] antialiased dark:bg-[linear-gradient(to_bottom,_#000000,_#111827)]`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          // enableSystem
          disableTransitionOnChange
        >
          <NavBar />
          {children}
          <Footer />
        </ThemeProvider>
      </body>
    </html>
  );
}
