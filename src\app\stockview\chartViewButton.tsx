export const ChartViewButton = ({
  label,
  isChartSelected,
}: {
  label: string;
  isChartSelected: Array<string>;
}) => {
  return (
    <div
      className={`h-6 rounded-[20px] sm:h-7 ${
        isChartSelected.includes(label) ? "bg-[#2182F299]" : "bg-[#1F2937]"
      } px-2 py-1 text-center text-xs font-normal text-[var(--dark-text)] sm:px-2.5 sm:text-sm`}
    >
      {label}
    </div>
  );
};
