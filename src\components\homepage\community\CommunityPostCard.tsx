"use client";

import { PostCardProps } from "@/types/homepage";
import { <PERSON><PERSON><PERSON>ork, MessageSquare, ThumbsUp } from "lucide-react";
import Link from "next/link";
import { motion } from "motion/react";
import { useState } from "react";

const CommunityPostCard = ({
  authorInitials,
  authorName,
  authorStatus,
  timestamp,
  content,
  likes,
  comments,
  shares,
  discussionLink,
}: PostCardProps) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);

  return (
    <motion.div
      className="rounded-lg border-2 border-[var(--dark-border)] bg-[#1A1A1A4D] p-4 sm:p-5"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      animate={{
        boxShadow: isHovered
          ? "0 0 8px 2px rgba(255, 255, 255, 0.2)"
          : "0 0 12px 0px rgba(255, 255, 255, 0.0)",
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Card Header */}
      <div className="flex items-start gap-3">
        <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-[var(--color-company)] text-lg font-bold text-white">
          {authorInitials}
        </div>
        <div className="flex-grow">
          <div className="flex flex-wrap items-center gap-x-2">
            <span className="text-sm font-semibold text-white sm:text-base">
              {authorName}
            </span>
            {authorStatus && (
              <span className="mt-1 rounded-full bg-[#082B15] px-2.5 py-0.5 text-xs font-medium text-[var(--color-success)] sm:mt-0">
                {authorStatus}
              </span>
            )}
          </div>
          <p className="text-xs text-[var(--dark-text-muted)] sm:text-sm">
            {timestamp}
          </p>
        </div>
      </div>

      {/* Card Body */}
      <p className="mt-4 text-sm break-words text-[var(--dark-text)]">
        {content}
      </p>

      {/* Card Footer */}
      <div className="mt-4 flex flex-wrap items-center gap-4 pt-4 sm:gap-6">
        <div className="flex items-center gap-1.5 text-sm text-[var(--dark-text-muted)]">
          <ThumbsUp size={18} />
          <span>{likes}</span>
        </div>
        <div className="flex items-center gap-1.5 text-sm text-[var(--dark-text-muted)]">
          <MessageSquare size={18} />
          <span>{comments}</span>
        </div>
        <div className="flex items-center gap-1.5 text-sm text-[var(--dark-text-muted)]">
          <GitFork size={18} />
          <span>{shares}</span>
        </div>
        <Link
          href={discussionLink}
          className="ml-auto text-sm font-medium text-[var(--dark-text)] transition-colors hover:text-[var(--color-company)]"
        >
          View Discussion
        </Link>
      </div>
    </motion.div>
  );
};

export default CommunityPostCard;
