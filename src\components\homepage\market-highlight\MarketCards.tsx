"use client";

import { useState } from "react";
import { motion } from "motion/react";
import { TrendingDown, TrendingUp } from "lucide-react";

const MarketCards = ({ isProfit }: { isProfit: boolean }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };
  return (
    <div>
      {/* Main NEPSE Card */}
      <motion.div
        className="relative flex h-32 flex-col items-center justify-center overflow-hidden rounded-xl bg-gradient-to-b from-black to-[#111827] md:w-[305.5px]"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        initial={{ scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        {/* Animated blur element */}
        <motion.div
          className="absolute h-20 w-20 rounded-full opacity-60"
          animate={{
            x: isHovered ? -60 : -60,
            y: isHovered ? -60 : 40,
            backgroundColor: isHovered
              ? isProfit
                ? "rgba(34, 197, 94, 0.4)"
                : "rgba(239, 68, 68, 0.4)"
              : "rgba(255, 255, 255, 0.3)",
            scale: isHovered ? 1.2 : 1,
          }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
          style={{
            filter: "blur(20px)",
            left: "10%",
            bottom: "10%",
          }}
        />

        {/* Additional blur layer for more depth */}
        <motion.div
          className="absolute h-16 w-16 rounded-full opacity-40"
          animate={{
            x: isHovered ? -40 : -40,
            y: isHovered ? -40 : 20,
            backgroundColor: isHovered
              ? isProfit
                ? "rgba(34, 197, 94, 0.2)"
                : "rgba(239, 68, 68, 0.2)"
              : "rgba(255, 255, 255, 0.2)",
          }}
          transition={{ duration: 0.6, ease: "easeInOut", delay: 0.1 }}
          style={{
            filter: "blur(25px)",
            left: "15%",
            bottom: "15%",
          }}
        />

        {/* Animated border */}
        <motion.div
          className="absolute inset-0 rounded-xl border-2 border-[var(--dark-border)]"
          animate={{
            borderColor: isHovered
              ? isProfit
                ? "rgba(34, 197, 94, 0.5)"
                : "rgba(239, 68, 68, 0.5)"
              : "rgba(75, 85, 99, 0.3)",
          }}
          transition={{ duration: 0.3 }}
        />

        {/* Content */}
        <div className="relative z-10 text-center">
          <h1 className="mb-2 text-sm text-gray-400">NEPSE</h1>

          <div className="mb-3 text-xl text-white">2,587.24</div>

          <div
            className={`flex items-center justify-center gap-1 ${isProfit ? "text-green-500" : "text-red-500"}`}
          >
            <span>{isProfit ? <TrendingUp /> : <TrendingDown />}</span>
            <span className="text-sm">+2.14%</span>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default MarketCards;
