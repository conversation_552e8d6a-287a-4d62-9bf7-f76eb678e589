"use client";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import { useTheme } from "next-themes";

const NavBar = () => {
  const { theme, setTheme } = useTheme();
  const [selectedLanguage, setSelectedLanguage] = useState<string>("EN");
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] =
    useState<boolean>(false);

  const languages = [
    { code: "EN", name: "English" },
    { code: "NP", name: "Nepali" },
  ];

  const handleLanguageSelect = (languageCode: string) => {
    setSelectedLanguage(languageCode);
    setIsLanguageDropdownOpen(false);
  };

  return (
    <div className="fixed inset-0 z-40 h-[64px] w-full border-b border-white/20 bg-white/10 px-[32px] py-[4px] shadow-lg md:h-[82px]">
      <div className="flex h-full items-center justify-between">
        <div className="group cursor-pointer transition-transform duration-300 hover:scale-105">
          <Link href="/">
            <Image
              src="/logo-dark.svg"
              width={44}
              height={44}
              alt="logo"
              className="h-[42px] w-[46px] drop-shadow-lg filter transition-all duration-300 group-hover:drop-shadow-xl md:h-auto md:w-auto"
            />
          </Link>
        </div>
        <div className="flex items-center justify-between space-x-6">
          <button
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            className="group relative overflow-hidden rounded-xl p-2 backdrop-blur-md transition-all duration-300 hover:scale-110"
          >
            {theme === "dark" ? (
              <Image
                src="/header/dm.svg"
                alt="dm toggle"
                width={36}
                height={36}
                className="relative z-10 h-[30px] w-[30px] transition-all duration-300 group-hover:brightness-110 md:h-auto md:w-full"
              />
            ) : (
              <Image
                src="/header/lm.svg"
                alt="lm toggle"
                width={36}
                height={36}
                className="relative z-10 h-[30px] w-[30px] transition-all duration-300 group-hover:brightness-110 md:h-auto md:w-full"
              />
            )}
          </button>

          {/* Language Toggle Dropdown */}
          <div className="relative">
            <button
              className="group relative flex h-[38px] w-[52px] items-center justify-center overflow-hidden rounded-md border border-white/20 bg-white/10 text-sm font-semibold text-white backdrop-blur-md transition-all duration-300 hover:scale-105 hover:bg-white/20 md:h-[44px] md:w-[72px]"
              onClick={() => setIsLanguageDropdownOpen(!isLanguageDropdownOpen)}
            >
              <span className="relative z-10 tracking-wide">
                {selectedLanguage}
              </span>
              <svg
                className={`relative z-10 ml-1 h-3 w-3 transition-all duration-300 ${
                  isLanguageDropdownOpen ? "rotate-180" : ""
                } group-hover:scale-110`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2.5}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {/* Dropdown Menu */}
            {isLanguageDropdownOpen && (
              <div className="absolute top-full right-0 z-50 mt-2 w-[140px] overflow-hidden rounded-xl border border-white/20 bg-white/10 shadow-xl backdrop-blur-xl">
                {languages.map((language, index) => (
                  <button
                    key={language.code}
                    className={`group relative w-full overflow-hidden px-4 py-3 text-left transition-all duration-300 hover:bg-white/20 ${
                      index === 0 ? "rounded-t-xl" : ""
                    } ${index === languages.length - 1 ? "rounded-b-xl" : ""}`}
                    onClick={() => handleLanguageSelect(language.code)}
                  >
                    <div className="relative z-10 text-sm font-medium text-white/90 transition-colors duration-300 group-hover:text-white">
                      {language.name}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavBar;
