"use client";
import { PrimaryButton } from "@/components/ui/common/buttons";
import { Container } from "@/components/ui/common/container";
import { RefreshCw, Star } from "lucide-react";
import { Inter } from "next/font/google";
import Image from "next/image";
import { useState } from "react";
import KeyMetrics from "./keymetrics";
import StockViewCommunityPosts from "./stockviewcommunityposts";
import Link from "next/link";
import RelatedNews from "./relatednews";
import TradingIdeas from "./tradingideas";
import TechnicalAnalysis from "./technicalAnalysis";
import StockViewSidebar from "./stockviewsidebar";
import { ChartViewButton } from "./chartViewButton";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});
const StockView = () => {
  const [isChartSelected, setIsChartSelected] = useState<Array<string>>([
    "Overview",
  ]);

  //   Chart Selection Buttons Lists
  const ChartSelectionButton = [
    "Overview",
    "News",
    "Ideas",
    "Discussion",
    "Technical",
    "Markets",
  ];

  // Chart Buttons Selection Handler Function
  const ChartSelectionHandler = (item: string) => {
    if (isChartSelected.includes(item)) {
      setIsChartSelected(isChartSelected.filter((i) => i !== item));
    } else {
      setIsChartSelected([...isChartSelected, item]);
    }
  };

  return (
    <Container className="col-span-12 mt-25 grid min-h-screen grid-cols-12 gap-4 px-4 py-3 sm:px-8">
      {/* Main Column Section - 9 columns */}
      <main className="col-span-12 lg:col-span-9">
        <header className="w-full">
          {/* Script Name */}
          <div className="flex w-full flex-row items-center justify-between gap-2">
            <h1 className="text-center text-xl font-semibold text-[var(--dark-text)] uppercase">
              NABIL{" "}
              <span
                className={`${inter.className} block text-lg font-bold text-[var(--dark-text-muted)] capitalize sm:inline`}
              >
                - Nabil Bank Ltd
              </span>
            </h1>
            <Star className="h-5 w-5 text-[var(--dark-text)]" />
          </div>

          {/* Script Price & Charts */}
          <div className="mt-2 flex flex-row items-center justify-between gap-3">
            <div className="text-center sm:text-left">
              <h1 className="text-lg font-semibold text-[var(--color-success)]">
                Rs.1,250.5
              </h1>
              <p className="sm:text-md text-sm font-normal text-[var(--color-success)]">
                +25.50 (+2.08%){" "}
                <span className="text-[var(--dark-text)]">Live</span>
              </p>
            </div>
            <button onClick={() => console.log("Add to Watchlist")}>
              <PrimaryButton label="Super Chart" icon="/stockview/chart.svg" />
            </button>
          </div>
        </header>

        {/* Chart View With Buttons Selections */}
        <section className="mt-10 sm:mt-20">
          {/* Buttons Selections Container  */}
          <div className="mb-4 flex flex-wrap items-center justify-center gap-2 sm:mb-6 sm:gap-4 md:justify-start">
            {ChartSelectionButton.map((item, idx) => (
              <button
                key={idx}
                type="button"
                onClick={() => ChartSelectionHandler(item)}
              >
                <ChartViewButton
                  label={item}
                  isChartSelected={isChartSelected}
                />
              </button>
            ))}
          </div>

          {/* Chart View Element Comes Over Here */}
          <div
            className="h-fit w-full overflow-hidden rounded-md border border-[var(--dark-border)] p-2 sm:p-3"
            style={{ background: "var(--dark-bg)" }}
          >
            <Image
              src="/stockview/live-chart.svg"
              alt="chart"
              width={866}
              height={532}
              className="h-auto w-full object-contain py-1.5"
            />
          </div>
        </section>

        {/* Key Metrics Section */}
        <section className="mt-12 pt-8 pb-14">
          <KeyMetrics />
        </section>

        {/* Community Posts with Swiper */}
        <section className="overflow-hidden pt-6 pb-14">
          <h2 className="mb-4 text-base font-medium text-[var(--dark-text)]">
            <span className="text-[var(--color-company)]">Community Posts</span>{" "}
            for NABIL
          </h2>

          <div className="w-full overflow-hidden">
            <StockViewCommunityPosts />
          </div>
          <Link
            href="/community"
            className="mt-5 flex items-center justify-center"
          >
            <button className="cursor-pointer rounded-md px-4 py-1.5 text-center text-sm font-normal text-[var(--dark-text)]">
              View All
            </button>
          </Link>
        </section>

        {/* Related News Container with Swiper Js */}
        <section className="overflow-hidden pb-14">
          <header className="flex items-center justify-between">
            <h2 className="mb-4 text-base font-medium text-[var(--dark-text)]">
              Related <span className="text-[var(--color-company)]">News</span>
            </h2>
            <button
              className="flex cursor-pointer items-center gap-2 text-sm text-[var(--dark-text)]"
              onClick={() => alert("News Refreshed")}
            >
              <RefreshCw className="h-5 w-5 text-[var(--color-company)]" />{" "}
              Reload news updates
            </button>
          </header>
          <div className="w-full overflow-hidden">
            <RelatedNews />
          </div>

          <Link href="/news" className="mt-5 flex items-center justify-end">
            <button className="cursor-pointer rounded-md px-4 py-1.5 text-center text-sm font-normal text-[var(--dark-text)]">
              View All
            </button>
          </Link>
        </section>

        {/* Trading Ideas with Swiper Js */}
        <section className="overflow-hidden pb-14">
          <h2 className="mb-4 text-base font-medium text-[var(--dark-text)]">
            Trading <span className="text-[var(--color-company)]">Ideas</span>
          </h2>

          <div className="w-full overflow-hidden">
            <TradingIdeas />
          </div>

          <Link
            href="/trading-ideas"
            className="mt-5 flex items-center justify-end"
          >
            <button className="cursor-pointer rounded-md px-4 py-1.5 text-center text-sm font-normal text-[var(--dark-text)]">
              View All
            </button>
          </Link>
        </section>

        {/* Technical Analysis Section */}
        <section className="overflow-hidden pb-14">
          <div className="flex items-center justify-between">
            <h4 className="text-base font-medium text-[var(--dark-text)]">
              Technical Analysis
            </h4>
            <p className="text-sm font-normal text-[var(--dark-text)]">
              Explore Full Technicals
            </p>
          </div>
          <div className="mt-1.5">
            <TechnicalAnalysis />
          </div>
        </section>

        {/* Seasonal Patterns Section */}
        <section className="overflow-hidden pb-14">
          <h2 className="mb-4 text-base font-medium text-[var(--dark-text)]">
            Seasonal{" "}
            <span className="text-[var(--color-company)]">Patterns</span> In
            NABIL
          </h2>

          <div className="rounded-sm border border-[var(--dark-border)] p-3">
            {/* Currently i am using image which contain border bottom but when we use api we need to wrap that api inside this div */}
            {/* <div className="border-b-1 border-[var(--dark-border)]"></div> */}
            <Image
              src="/stockview/bar.svg"
              alt="seasonal-patterns"
              width={1000}
              height={500}
              className="h-auto w-full object-contain"
            />

            <p className="mt-2 text-sm text-[var(--dark-text-muted)]">
              Average monthly returns over the past 5 years
            </p>
          </div>
        </section>
      </main>

      {/* Sidebar Column - 2 columns */}
      <aside className="col-span-12 lg:col-span-3">
        <div className="sticky top-22 space-y-6">
          <StockViewSidebar />
        </div>
      </aside>
    </Container>
  );
};

export default StockView;
