import { SentimentBarProps } from "@/types/homepage";

const SentimentBar = ({ value, color }: SentimentBarProps) => {
  const barColor = color === '--color-success' ? 'bg-[var(--color-success)]' : 'bg-[var(--color-error)]';

  return (
    // Outer container for the bar background
    <div className="h-7 w-full rounded-md bg-[#111827CC]">
      {/* Inner div for the colored fill, width is dynamic */}
      <div
        className={`flex h-full items-center justify-center rounded-md text-xs font-normal text-[var(--dark-text)] ${barColor}`}
        style={{ width: `${value}%` }}
      >
        {value}%
      </div>
    </div>
  );
};

export default SentimentBar;