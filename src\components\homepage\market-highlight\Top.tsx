"use client";

import { topGainersData, topLosersData } from "@/app/constants";
import { Item } from "@/types/homepage";
import { TrendingDown, TrendingUp } from "lucide-react";
import { motion } from "motion/react";

const ListItem = ({ item, colorClass }: { item: Item; colorClass: string }) => (
  <div className="flex items-center justify-between rounded-lg bg-[#1F1F1F] p-3">
    {/* Left Side: Name and Full Name */}
    <div>
      <p className="font-bold text-white">{item.name}</p>
      <p className="text-sm text-gray-400">{item.fullName}</p>
    </div>
    {/* Right Side: Change and Price */}
    <div className="text-right">
      <p className={`font-semibold ${colorClass}`}>{item.change}</p>
      <p className="text-sm text-gray-400">{item.price}</p>
    </div>
  </div>
);

const Top = ({ isGainer }: { isGainer: boolean }) => {
  const title = isGainer ? "Top Gainers" : "Top Losers";
  const Icon = isGainer ? TrendingUp : TrendingDown;
  const colorClass = isGainer
    ? "text-[var(--color-success)]"
    : "text-[var(--color-error)]";

  const data = isGainer ? topGainersData : topLosersData;

  const cardVariants = {
    rest: {
      borderLeftColor: "rgba(0, 0, 0, 0)",
    },
    hover: {
      borderLeftWidth: 4,
      borderLeftColor: isGainer
        ? "rgba(34, 197, 94, 0.8)"
        : "rgba(239, 68, 68, 0.8)",
    },
  };

  return (
    <>
      <style jsx global>{`
        .no-scrollbar-webkit::-webkit-scrollbar {
          display: none;
        }
      `}</style>

      <motion.div
        className="w-full overflow-hidden rounded-xl border border-l-4 border-[var(--dark-border)] border-l-transparent bg-[#111827] p-4 text-white sm:p-6"
        style={{
          background: "var(--dark-bg)",
        }}
        initial="rest"
        whileHover="hover"
        animate="rest"
        variants={cardVariants}
        transition={{ duration: 0.4, ease: "easeInOut" }}
      >
        {/* Card Header */}
        <div className={`flex items-center gap-2 ${colorClass}`}>
          <Icon size={24} />
          <h1 className="text-2xl font-semibold text-white">{title}</h1>
        </div>

        {/* Scrollable Card Content */}
        <div
          className="no-scrollbar-webkit mt-4 h-60 overflow-y-auto"
          style={{
            msOverflowStyle: "none",
            scrollbarWidth: "none",
          }}
        >
          <div className="mt-2 flex flex-col gap-3">
            {data.map((item) => (
              <ListItem key={item.id} item={item} colorClass={colorClass} />
            ))}
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default Top;
