name: <PERSON>uild, Push, and Deploy Frontend

on:
  push:
    branches: [main]

jobs:
  build-and-push:
    outputs:
      git_sha: ${{ steps.vars.outputs.sha_short }}
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v3

      - name: Set Variables
        id: vars
        run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Log in to Private Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.REGISTRY_HOST }}
          username: ${{ secrets.REGISTRY_USER }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build and Push Frontend Image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: |
            ${{ secrets.REGISTRY_HOST }}/chartnp-frontend:${{ steps.vars.outputs.sha_short }}
            ${{ secrets.REGISTRY_HOST }}/chartnp-frontend:latest

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          script: |
           # 1. Log in to the private Docker registry on the server
            echo ${{ secrets.REGISTRY_PASSWORD }} | docker login ${{ secrets.REGISTRY_HOST }} -u ${{ secrets.REGISTRY_USER }} --password-stdin
            
            # 2. Navigate to the directory where docker-compose.yml lives
            cd /home/<USER>/chartnp/chartnp_backend/
            
            # 3. Pull the new version of the frontend image from the registry
            docker-compose pull frontend

            # 4. nuke the older conatainers     
            docker-compose down --remove-orphans


            # 5. Restart the stack. Docker Compose will use the existing docker-compose.yml
            docker-compose up -d --force-recreate --remove-orphans
