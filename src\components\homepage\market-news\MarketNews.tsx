"use client";

import { newsData } from "@/app/constants";
import { NewsData } from "@/types/homepage";
import { ArrowRight } from "lucide-react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Swiper, SwiperSlide } from "swiper/react";
import NewsCard from "./NewsCard";

const MarketNews = () => {
  return (
    <div className="mt-10 px-4 sm:px-6 md:px-12">
      {/* Header */}
      <div className="flex flex-col items-start justify-between gap-4 p-4 text-white md:flex-row md:items-center">
        <div className="flex flex-col gap-2">
          <h1 className="text-2xl">
            Latest{" "}
            <span className="bg-gradient-to-r from-[#1A7EF2] to-[#6366F1] bg-clip-text text-transparent">
              Market News
            </span>
          </h1>
          <p>Stay updated with official NEPSE announcements and market news</p>
        </div>

        <button className="group flex items-center gap-2 rounded-lg bg-transparent px-4 py-2 transition-colors duration-400 hover:bg-[var(--dark-border)]">
          <span className="font-medium text-white group-hover:bg-gradient-to-r group-hover:from-[#1A7EF2] group-hover:to-[#6366F1] group-hover:bg-clip-text group-hover:text-transparent">
            View More
          </span>

          <ArrowRight
            size={18}
            className="text-white transition-colors duration-300 group-hover:text-[#1A7EF2]"
          />
        </button>
      </div>

      {/* News Cards */}
      <div className="mt-4 w-full overflow-hidden">
        <Swiper
          grabCursor={true}
          spaceBetween={25}
          breakpoints={{
            320: { slidesPerView: 1.1 },
            640: { slidesPerView: 1.5 },
            768: { slidesPerView: 2.2 },
            1024: { slidesPerView: 3.5 },
          }}
        >
          {newsData.map((news: NewsData) => (
            <SwiperSlide key={news.id} className="!h-auto">
              <NewsCard {...news} />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default MarketNews;
