"use client";

import { NewsData } from "@/types/homepage";
import { useState } from "react";
import { Clock, Newspaper } from "lucide-react";
import Link from "next/link";
import { motion } from "motion/react";

const NewsCard = ({
  category,
  time,
  title,
  description,
  source,
  link,
}: NewsData) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className="w-full md:my-5 max-w-sm rounded-xl border border-[var(--dark-border)] bg-[#1A1A1A1A] p-6 pb-14"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      animate={{
        boxShadow: isHovered
          ? "0 0 8px 2px rgba(255, 255, 255, 0.2)"
          : "0 0 12px 0px rgba(255, 255, 255, 0.0)",
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Top Section */}
      <div className="flex items-center justify-between">
        <span className="font-small rounded-md bg-[#0B5DBC33] px-3 py-1 text-sm text-[var(--color-company)]">
          {category}
        </span>
        <span className="flex items-center gap-2 text-sm text-[var(--dark-text-muted)]">
          <Clock size={16} />
          {time}
        </span>
      </div>

      {/* Content */}
      <div className="mt-4 flex-grow">
        <h2 className="line-clamp-2 text-base sm:text-lg font-medium text-[var(--dark-text)]">
          {title}
        </h2>
        <p className="mt-2 line-clamp-3 text-sm sm:text-base font-normal text-[var(--dark-text-muted)]">
          {description}
        </p>
      </div>

      {/* Footer */}
      <div className="mt-6 flex items-center justify-between">
        <span className="flex items-center gap-2 text-sm text-[var(--dark-text-muted)]">
          <Newspaper size={16} />
          {source}
        </span>
        <Link
          href={link}
          className="text-sm font-normal text-[var(--dark-text)] underline underline-offset-4"
        >
          Read More
        </Link>
      </div>
    </motion.div>
  );
};

export default NewsCard;
