// src/components/CommunityPosts.tsx
"use client";

import { useEffect, useState } from "react";
import { Post } from "@/types/community"; // Make sure this type definition matches your data structure
import Image from "next/image";

export default function CommunityPost() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        const response = await fetch(
          "https://api.aayush786.xyz/api/community/posts",
        );
        if (!response.ok) {
          throw new Error("Failed to fetch posts");
        }
        const data = await response.json();
        setPosts(data);
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (isLoading) return <p>Loading posts...</p>;
  if (error) return <p>Error: {error}</p>;

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">Community Posts</h1>
      {posts.map((post) => (
        <div key={post.id} className="rounded-lg border p-4 shadow-sm">
          <h2 className="text-xl font-semibold">{post.title}</h2>
          <p className="text-sm text-gray-500">
            {/* By {post.user.firstname} on{" "} */} sakar
            {new Date(post.created_at).toLocaleDateString()}
          </p>
          <p className="mt-2">{post.content}</p>
          {post.chart_image && (
            <div className="mt-4">
              <Image
                src={post.chart_image}
                alt="Chart Image"
                width={795}
                height={328}
                className="h-[328px] w-full rounded-lg border border-[var(--dark-border)] object-cover"
              />
              {/* In a real application, you would render the image here */}
              <p className="text-sm text-gray-600">
                Chart Analysis: {post.analysis_text}
              </p>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
