"use client";

import { motion, Variants } from "motion/react";
import { Search } from "lucide-react";

const containerVariants: Variants = {
  initial: {
    borderRadius: "50%",
    justifyContent: "center",
    alignItems: "center",
  },
  hover: {
    borderRadius: "25%",
    // backgroundColor: "#000000",
    justifyContent: "flex-end",
    padding: "0",
    // alignItems: "flex-end",
  },
};

const bgVariants: Variants = {
  initial: { backgroundColor: "#041F3F" },
  hover: { backgroundColor: "#000000" },
};

const SearchButton = () => {
  return (
    // This button is now the flex container that changes its alignment
    <motion.button
      className="relative flex h-[38px] w-[38px] bg-gradient-to-b from-[#1A7EF2] to-[#6366F1] p-1.5"
      initial="initial"
      whileHover="hover"
      variants={containerVariants}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Inner background span */}
      <motion.span
        className="absolute inset-[1px] rounded-[inherit]"
        variants={bgVariants}
      />

      {/* The Icon itself, which will be moved by the parent's flex properties */}
      <motion.div
        className="relative z-10"
        layout // This is the magic! It animates the position change smoothly.
      >
        <Search size={20} className="text-[#1A7EF2]" />
      </motion.div>
    </motion.button>
  );
};

export default SearchButton;
