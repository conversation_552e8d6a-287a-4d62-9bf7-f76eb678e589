import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/free-mode";
import { RelatedNewsProps } from "@/types/stockdetails";
import { Clock, MessageSquare } from "lucide-react";
import { FreeMode, Mousewheel } from "swiper/modules";
import { LikeButton, ShareButton } from "@/components/ui/common/buttons";

const RelatedNews = () => {
  const relatedNewsData = [
    {
      avatar: "/stockview/share-sansar.jpg",
      name: "Share Sansar",
      time: "Today",
      content: "Nabil Bank announces 18% dividend for fiscal year 2080/81",
      tags: "Bullish",
    },
    {
      avatar: "/stockview/mero-lagani.png",
      name: "Mero Lagani",
      time: "3 hrs ago",
      content:
        "<PERSON>bil’s Q3 EPS jumps to Rs. 25.05, beating analyst expectations",
      tags: "Bullish",
    },
    {
      avatar: "/stockview/biz.png",
      name: "BizNepal",
      time: "2 hrs ago",
      content:
        "Banking sector liquidity tightens, but Nabil maintains strong CD ratio",
      tags: "Neutral",
    },
    {
      avatar: "/stockview/opi.png",
      name: "Nepal Stock Exchange",
      time: "4 hrs ago",
      content: "Nabil stock finds support at Rs. 1220, traders eye breakout",
      tags: "Bullish",
    },
    {
      avatar: "/stockview/mero-lagani.png",
      name: "Mero Lagani",
      time: "30 min ago",
      content:
        "Nabil’s Q3 EPS jumps to Rs. 25.05, beating analyst expectations",
      tags: "Bear",
    },
  ];
  return (
    <Swiper
      modules={[FreeMode, Mousewheel]}
      spaceBetween={14}
      slidesPerView="auto"
      freeMode={true}
      mousewheel={{
        forceToAxis: true,
        sensitivity: 1,
        releaseOnEdges: true,
      }}
      grabCursor={true}
      className="related-news-swiper"
    >
      {relatedNewsData.map((item, idx) => (
        <SwiperSlide key={idx} style={{ width: "auto" }}>
          <RelatedNewsCard
            avatar={item.avatar}
            name={item.name}
            timestamp={item.time}
            content={item.content}
            tags={item.tags}
          />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default RelatedNews;

export const RelatedNewsCard = ({
  avatar,
  name,
  timestamp,
  content,
  tags,
}: RelatedNewsProps) => {
  return (
    <div className="flex w-full max-w-[322px] flex-col gap-6 rounded-md border border-[var(--dark-border)] bg-[var(--dark-surface)] p-3">
      {/* Top Section: Tag and Timestamp */}
      <div className="flex items-center gap-3">
        <div className="h-12 w-12 overflow-hidden rounded-full">
          <Image
            src={avatar}
            alt={name}
            width={40}
            height={40}
            className="object-content h-auto w-full"
          />
        </div>
        <div className="text-sm text-[var(--dark-text)]">
          <p>{name}</p>
          <div className="flex items-center gap-1 text-sm text-[var(--dark-text-muted)]">
            <Clock size={16} />
            {timestamp}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-grow">
        <p className="mt-2 mb-1 line-clamp-2 text-base font-normal text-[var(--dark-text)]">
          {content}
        </p>

        {/* Tags Container According to changes between "Bullish", "Bear", "Neutral" */}
        <div className="flex flex-wrap gap-2">
          {tags === "Bullish" && (
            <span className="rounded-4xl bg-[#0F4135] px-3 py-0.5 text-xs text-[var(--color-success)]">
              Bullish
            </span>
          )}
          {tags === "Neutral" && (
            <span className="rounded-4xl bg-[#292D34] px-3 py-0.5 text-xs text-[var(--dark-text-muted)]">
              Neutral
            </span>
          )}

          {tags === "Bear" && (
            <span className="rounded-4xl bg-[#410f1e] px-3 py-0.5 text-xs text-[var(--color-error)]">
              Bear
            </span>
          )}
        </div>
      </div>

      {/* Footer Section: Source and Read More Link */}
      <div className="flex items-center justify-between border-t border-[var(--dark-border)] px-2 py-3">
        {/* Reaction Buttons  */}
        <div className="flex items-center gap-3">
          {/* Like Container */}
          <LikeButton />

          {/* Comments Container */}
          <div className="flex items-center gap-1 text-[var(--dark-text-muted)]">
            <MessageSquare className="h-5 w-5" />{" "}
            <span className="text-sm">48</span>
          </div>

          {/* Share Container */}
          <ShareButton />
        </div>

        {/* View Full Articls Buttons */}
        <button
          className="text-sm text-[var(--dark-text)]"
          onClick={() => alert("View Full Article Pressed")}
        >
          View Full Article
        </button>
      </div>
    </div>
  );
};
